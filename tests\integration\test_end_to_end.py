"""End-to-end integration tests for the trading engine."""

import pytest
import time
from datetime import datetime, timedelta
from unittest.mock import patch

from src.orchestrator import TradingOrchestrator
from src.broker import PaperBroker
from src.data_feed import HistoricalFeed
from src.strategies import StrategyEngine, Signal, SignalType
from src.risk import RiskManager
from src.utils import config


class TestTradingEngineIntegration:
    """Integration tests for the complete trading engine."""
    
    def test_paper_trading_initialization(self, sample_symbols, sample_ohlcv_data):
        """Test complete paper trading engine initialization."""
        # Force paper mode
        with patch.object(config, 'mode', 'paper'):
            orchestrator = TradingOrchestrator(sample_symbols)
            
            success = orchestrator.initialize()
            assert success
            
            # Check all components are initialized
            assert orchestrator.broker is not None
            assert isinstance(orchestrator.broker, PaperBroker)
            assert orchestrator.broker.is_connected()
            
            assert orchestrator.strategy_engine is not None
            assert len(orchestrator.strategy_engine.strategies) > 0
            
            assert orchestrator.risk_manager is not None
            assert orchestrator.data_feed_manager is not None
            
            orchestrator.stop()
    
    def test_strategy_signal_generation_flow(self, sample_symbols, mock_market_data_stream):
        """Test the flow from market data to signal generation."""
        # Create components
        broker = PaperBroker(initial_capital=10000.0)
        broker.connect()
        
        strategy_engine = StrategyEngine()
        strategy_engine.create_default_strategies(sample_symbols)
        
        risk_manager = RiskManager(broker)
        
        # Generate some market data and process
        data_stream = mock_market_data_stream(sample_symbols, num_periods=5)
        signals_generated = []
        
        for market_data in data_stream:
            # Update strategies with market data
            signals = strategy_engine.execute_strategies(market_data)
            signals_generated.extend(signals)
            
            # Process signals through risk management
            for signal in signals:
                should_execute, position_size, reason = risk_manager.evaluate_signal(
                    signal, market_data.data[signal.symbol].to_dict()
                )
                
                if should_execute:
                    # Execute the signal
                    signal.quantity = position_size
                    order_args = signal.to_order_args()
                    order = broker.place_order(**order_args)
                    
                    # Update broker with market data for order processing
                    broker.update_market_data(
                        market_data.timestamp,
                        {symbol: ohlcv.to_dict() for symbol, ohlcv in market_data.data.items()}
                    )
        
        # Verify the flow worked
        assert len(strategy_engine.strategies) > 0
        
        # Check that broker processed some activity
        portfolio_value = broker.get_portfolio_value()
        assert portfolio_value > 0
    
    def test_risk_management_integration(self, sample_symbols, sample_market_data):
        """Test risk management integration with broker and strategies."""
        broker = PaperBroker(initial_capital=10000.0)
        broker.connect()
        
        risk_manager = RiskManager(broker)
        
        # Create a signal that should be approved
        signal = Signal(
            symbol='AAPL',
            signal_type=SignalType.BUY,
            timestamp=datetime.now(),
            price=150.0,
            confidence=0.8,
            stop_loss=145.0
        )
        
        # Update broker with market data
        broker.update_market_data(
            sample_market_data.timestamp,
            {symbol: ohlcv.to_dict() for symbol, ohlcv in sample_market_data.data.items()}
        )
        
        # Evaluate signal
        should_execute, position_size, reason = risk_manager.evaluate_signal(
            signal, sample_market_data.data['AAPL'].to_dict()
        )
        
        assert should_execute
        assert position_size > 0
        
        # Execute the signal
        signal.quantity = position_size
        order_args = signal.to_order_args()
        order = broker.place_order(**order_args)
        
        # Process the order
        broker._process_pending_orders()
        
        # Verify position was created
        position = broker.get_position('AAPL')
        assert position is not None
        assert position.quantity == position_size
        
        # Add risk controls
        success = risk_manager.add_position_risk_controls(
            symbol='AAPL',
            position=position,
            signal=signal
        )
        assert success
        
        # Verify stop loss was added
        stop_info = risk_manager.stop_loss_manager.get_stop_info('AAPL')
        assert stop_info is not None
        assert stop_info['is_active']
    
    def test_portfolio_monitoring_integration(self, sample_symbols):
        """Test portfolio monitoring with real trading activity."""
        broker = PaperBroker(initial_capital=10000.0)
        broker.connect()
        
        risk_manager = RiskManager(broker)
        
        # Simulate some trading activity
        initial_value = broker.get_portfolio_value()
        
        # Update portfolio monitoring
        positions = broker.get_positions()
        snapshot = risk_manager.portfolio_monitor.update_portfolio(
            total_value=initial_value,
            cash_balance=broker.cash_balance,
            positions=positions
        )
        
        assert snapshot.total_value == initial_value
        assert snapshot.cash_balance == broker.cash_balance
        assert snapshot.positions_count == 0
        
        # Get risk metrics
        metrics = risk_manager.portfolio_monitor.get_risk_metrics()
        assert 'total_value' in metrics
        assert metrics['total_value'] == initial_value
    
    def test_stop_loss_execution_flow(self, sample_symbols, sample_market_data):
        """Test complete stop loss execution flow."""
        broker = PaperBroker(initial_capital=10000.0)
        broker.connect()
        
        risk_manager = RiskManager(broker)
        
        # Update broker with market data
        broker.update_market_data(
            sample_market_data.timestamp,
            {symbol: ohlcv.to_dict() for symbol, ohlcv in sample_market_data.data.items()}
        )
        
        # Create and execute a buy order
        order = broker.place_order(
            symbol='AAPL',
            side='buy',
            order_type='market',
            quantity=100
        )
        
        broker._process_pending_orders()
        
        # Get the position
        position = broker.get_position('AAPL')
        assert position is not None
        
        # Add stop loss
        success = risk_manager.stop_loss_manager.add_stop_loss(
            symbol='AAPL',
            position=position,
            stop_type='fixed',
            stop_price=140.0  # Below current price
        )
        assert success
        
        # Simulate price drop to trigger stop
        low_price_data = {
            'AAPL': {
                'timestamp': datetime.now(),
                'symbol': 'AAPL',
                'open': 135.0,
                'high': 136.0,
                'low': 134.0,
                'close': 135.0,  # Below stop price
                'volume': 1000000
            }
        }
        
        # Update risk monitoring
        stop_orders = risk_manager.update_risk_monitoring(low_price_data)
        
        # Should generate stop loss order
        assert len(stop_orders) > 0
        stop_order = stop_orders[0]
        assert stop_order.symbol == 'AAPL'
        assert stop_order.side.value == 'sell'  # Opposite of original position
    
    def test_backtest_execution(self, sample_symbols, sample_ohlcv_data):
        """Test complete backtest execution."""
        # Ensure we're in paper mode
        with patch.object(config, 'mode', 'paper'):
            with patch.object(config, 'backtest_start_date', '2023-01-01'):
                with patch.object(config, 'backtest_end_date', '2023-01-31'):
                    
                    orchestrator = TradingOrchestrator(sample_symbols)
                    
                    # Mock the data downloader to return our sample data
                    with patch('src.data_feed.data_downloader.DataDownloader.list_data_files') as mock_list:
                        with patch('src.data_feed.data_downloader.DataDownloader.load_data_file') as mock_load:
                            mock_list.return_value = ['test_data.csv']
                            mock_load.return_value = sample_ohlcv_data
                            
                            # Initialize the orchestrator
                            success = orchestrator.initialize()
                            assert success
                            
                            # Get initial portfolio value
                            initial_value = orchestrator.broker.get_portfolio_value()
                            assert initial_value > 0
                            
                            # Run a few iterations manually (simulating backtest)
                            primary_feed = orchestrator.data_feed_manager.get_primary_feed()
                            iterations = 0
                            max_iterations = 10
                            
                            for market_data in primary_feed.stream():
                                orchestrator._process_market_data(market_data)
                                iterations += 1
                                
                                if iterations >= max_iterations:
                                    break
                            
                            # Check that some processing occurred
                            assert orchestrator.total_signals_processed >= 0
                            assert orchestrator.last_update is not None
                            
                            # Get final status
                            status = orchestrator.get_status()
                            assert status['is_running']
                            assert 'portfolio_value' in status
                            
                            orchestrator.stop()
    
    def test_error_handling_and_recovery(self, sample_symbols):
        """Test error handling and recovery mechanisms."""
        broker = PaperBroker(initial_capital=10000.0)
        broker.connect()
        
        strategy_engine = StrategyEngine()
        risk_manager = RiskManager(broker)
        
        # Test invalid signal handling
        invalid_signal = Signal(
            symbol='INVALID',
            signal_type=SignalType.BUY,
            timestamp=datetime.now(),
            price=-100.0,  # Invalid negative price
            confidence=0.8
        )
        
        # Should handle gracefully
        should_execute, position_size, reason = risk_manager.evaluate_signal(
            invalid_signal, {}
        )
        
        # Should reject invalid signal
        assert not should_execute
        
        # Test emergency stop recovery
        risk_manager.trigger_emergency_stop("Test emergency")
        assert risk_manager.is_emergency_stop
        
        # Clear emergency stop
        risk_manager.clear_emergency_stop("Test recovery")
        assert not risk_manager.is_emergency_stop
    
    def test_performance_tracking(self, sample_symbols, mock_market_data_stream):
        """Test performance tracking across components."""
        broker = PaperBroker(initial_capital=10000.0)
        broker.connect()
        
        strategy_engine = StrategyEngine()
        strategy_engine.create_default_strategies(sample_symbols)
        
        risk_manager = RiskManager(broker)
        
        # Process several periods of data
        data_stream = mock_market_data_stream(sample_symbols, num_periods=10)
        
        for market_data in data_stream:
            # Execute strategies
            signals = strategy_engine.execute_strategies(market_data)
            
            # Update risk monitoring
            market_data_dict = {
                symbol: ohlcv.to_dict() for symbol, ohlcv in market_data.data.items()
            }
            risk_manager.update_risk_monitoring(market_data_dict, market_data.timestamp)
        
        # Check performance stats
        strategy_stats = strategy_engine.get_performance_stats()
        assert 'total_strategies' in strategy_stats
        assert 'total_signals_generated' in strategy_stats
        
        risk_status = risk_manager.get_risk_status()
        assert 'portfolio_metrics' in risk_status
        
        # Check broker performance if available
        if hasattr(broker, 'get_performance_stats'):
            broker_stats = broker.get_performance_stats()
            assert 'total_trades' in broker_stats
    
    def test_concurrent_operations(self, sample_symbols, sample_market_data):
        """Test thread safety and concurrent operations."""
        import threading
        
        broker = PaperBroker(initial_capital=10000.0)
        broker.connect()
        
        risk_manager = RiskManager(broker)
        results = []
        
        def process_signal(signal_id):
            """Process a signal in a separate thread."""
            signal = Signal(
                symbol='AAPL',
                signal_type=SignalType.BUY,
                timestamp=datetime.now(),
                price=150.0 + signal_id,  # Slightly different prices
                confidence=0.7
            )
            
            should_execute, position_size, reason = risk_manager.evaluate_signal(
                signal, sample_market_data.data['AAPL'].to_dict()
            )
            
            results.append((signal_id, should_execute, position_size, reason))
        
        # Create multiple threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=process_signal, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Check results
        assert len(results) == 5
        
        # At least some signals should be processed successfully
        successful_signals = [r for r in results if r[1]]  # should_execute = True
        assert len(successful_signals) >= 0  # May be 0 due to position limits
