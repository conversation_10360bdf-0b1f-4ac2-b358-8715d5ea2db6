"""Options analysis for EV/POP calculations."""

import math
from datetime import datetime, date
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import numpy as np
from scipy.stats import norm

from .base_strategy import Signal, SignalType
from ..utils import get_logger, config, calculate_days_to_expiry


@dataclass
class OptionsSignal(Signal):
    """Extended signal for options trading."""
    strike: float = 0.0
    expiry_date: date = None
    option_type: str = "call"  # "call" or "put"
    implied_volatility: float = 0.0
    delta: float = 0.0
    gamma: float = 0.0
    theta: float = 0.0
    vega: float = 0.0
    expected_value: float = 0.0
    probability_of_profit: float = 0.0


class OptionsAnalyzer:
    """Options analysis and EV/POP calculator."""
    
    def __init__(self):
        self.logger = get_logger("trading_engine.strategy.options")
        self.risk_free_rate = 0.02  # 2% risk-free rate
    
    def black_scholes_price(
        self,
        spot_price: float,
        strike_price: float,
        time_to_expiry: float,
        risk_free_rate: float,
        volatility: float,
        option_type: str = "call"
    ) -> float:
        """Calculate Black-Scholes option price.
        
        Args:
            spot_price: Current stock price
            strike_price: Option strike price
            time_to_expiry: Time to expiry in years
            risk_free_rate: Risk-free interest rate
            volatility: Implied volatility
            option_type: "call" or "put"
            
        Returns:
            Option price
        """
        if time_to_expiry <= 0:
            # At expiry
            if option_type == "call":
                return max(0, spot_price - strike_price)
            else:
                return max(0, strike_price - spot_price)
        
        d1 = (math.log(spot_price / strike_price) + 
              (risk_free_rate + 0.5 * volatility**2) * time_to_expiry) / (volatility * math.sqrt(time_to_expiry))
        d2 = d1 - volatility * math.sqrt(time_to_expiry)
        
        if option_type == "call":
            price = (spot_price * norm.cdf(d1) - 
                    strike_price * math.exp(-risk_free_rate * time_to_expiry) * norm.cdf(d2))
        else:
            price = (strike_price * math.exp(-risk_free_rate * time_to_expiry) * norm.cdf(-d2) - 
                    spot_price * norm.cdf(-d1))
        
        return max(0, price)
    
    def calculate_greeks(
        self,
        spot_price: float,
        strike_price: float,
        time_to_expiry: float,
        risk_free_rate: float,
        volatility: float,
        option_type: str = "call"
    ) -> Dict[str, float]:
        """Calculate option Greeks.
        
        Returns:
            Dictionary with delta, gamma, theta, vega
        """
        if time_to_expiry <= 0:
            return {'delta': 0, 'gamma': 0, 'theta': 0, 'vega': 0}
        
        d1 = (math.log(spot_price / strike_price) + 
              (risk_free_rate + 0.5 * volatility**2) * time_to_expiry) / (volatility * math.sqrt(time_to_expiry))
        d2 = d1 - volatility * math.sqrt(time_to_expiry)
        
        # Delta
        if option_type == "call":
            delta = norm.cdf(d1)
        else:
            delta = norm.cdf(d1) - 1
        
        # Gamma
        gamma = norm.pdf(d1) / (spot_price * volatility * math.sqrt(time_to_expiry))
        
        # Theta
        theta_part1 = -(spot_price * norm.pdf(d1) * volatility) / (2 * math.sqrt(time_to_expiry))
        theta_part2 = risk_free_rate * strike_price * math.exp(-risk_free_rate * time_to_expiry)
        
        if option_type == "call":
            theta = (theta_part1 - theta_part2 * norm.cdf(d2)) / 365
        else:
            theta = (theta_part1 + theta_part2 * norm.cdf(-d2)) / 365
        
        # Vega
        vega = spot_price * norm.pdf(d1) * math.sqrt(time_to_expiry) / 100
        
        return {
            'delta': delta,
            'gamma': gamma,
            'theta': theta,
            'vega': vega
        }
    
    def calculate_expected_value(
        self,
        spot_price: float,
        strike_price: float,
        option_price: float,
        time_to_expiry: float,
        volatility: float,
        option_type: str = "call",
        num_simulations: int = 10000
    ) -> float:
        """Calculate expected value using Monte Carlo simulation.
        
        Args:
            spot_price: Current stock price
            strike_price: Option strike price
            option_price: Current option price (premium paid)
            time_to_expiry: Time to expiry in years
            volatility: Expected volatility
            option_type: "call" or "put"
            num_simulations: Number of Monte Carlo simulations
            
        Returns:
            Expected value of the trade
        """
        if time_to_expiry <= 0:
            return 0
        
        # Monte Carlo simulation
        dt = time_to_expiry
        drift = (self.risk_free_rate - 0.5 * volatility**2) * dt
        diffusion = volatility * math.sqrt(dt)
        
        payoffs = []
        
        for _ in range(num_simulations):
            # Generate random price at expiry
            random_shock = np.random.normal(0, 1)
            price_at_expiry = spot_price * math.exp(drift + diffusion * random_shock)
            
            # Calculate payoff
            if option_type == "call":
                payoff = max(0, price_at_expiry - strike_price) - option_price
            else:
                payoff = max(0, strike_price - price_at_expiry) - option_price
            
            payoffs.append(payoff)
        
        return np.mean(payoffs)
    
    def calculate_probability_of_profit(
        self,
        spot_price: float,
        strike_price: float,
        option_price: float,
        time_to_expiry: float,
        volatility: float,
        option_type: str = "call"
    ) -> float:
        """Calculate probability of profit.
        
        Returns:
            Probability of profit as a percentage (0-100)
        """
        if time_to_expiry <= 0:
            return 0
        
        # Breakeven price
        if option_type == "call":
            breakeven = strike_price + option_price
        else:
            breakeven = strike_price - option_price
        
        # Calculate probability using log-normal distribution
        drift = (self.risk_free_rate - 0.5 * volatility**2) * time_to_expiry
        diffusion = volatility * math.sqrt(time_to_expiry)
        
        d = (math.log(breakeven / spot_price) - drift) / diffusion
        
        if option_type == "call":
            prob = 1 - norm.cdf(d)
        else:
            prob = norm.cdf(d)
        
        return prob * 100
    
    def analyze_option_chain(
        self,
        symbol: str,
        spot_price: float,
        option_chain: List[Dict[str, Any]],
        target_dte_range: Tuple[int, int] = (7, 45)
    ) -> List[OptionsSignal]:
        """Analyze option chain for trading opportunities.
        
        Args:
            symbol: Underlying symbol
            spot_price: Current stock price
            option_chain: List of option data
            target_dte_range: Target days to expiry range
            
        Returns:
            List of option signals
        """
        signals = []
        
        for option in option_chain:
            try:
                # Extract option data
                strike = float(option.get('strike', 0))
                expiry_date = option.get('expiry_date')
                option_type = option.get('type', 'call').lower()
                bid = float(option.get('bid', 0))
                ask = float(option.get('ask', 0))
                implied_vol = float(option.get('implied_volatility', 0.2))
                
                if not expiry_date or bid <= 0 or ask <= 0:
                    continue
                
                # Calculate days to expiry
                if isinstance(expiry_date, str):
                    expiry_date = datetime.strptime(expiry_date, '%Y-%m-%d').date()
                
                dte = calculate_days_to_expiry(expiry_date)
                
                # Filter by DTE
                if not (target_dte_range[0] <= dte <= target_dte_range[1]):
                    continue
                
                # Use mid price
                option_price = (bid + ask) / 2
                time_to_expiry = dte / 365.25
                
                # Calculate metrics
                ev = self.calculate_expected_value(
                    spot_price, strike, option_price, time_to_expiry, implied_vol, option_type
                )
                
                pop = self.calculate_probability_of_profit(
                    spot_price, strike, option_price, time_to_expiry, implied_vol, option_type
                )
                
                greeks = self.calculate_greeks(
                    spot_price, strike, time_to_expiry, self.risk_free_rate, implied_vol, option_type
                )
                
                # Filter by EV and POP thresholds
                if ev >= config.min_ev_threshold and pop >= config.min_pop_threshold:
                    
                    # Determine signal type based on option type and analysis
                    if option_type == "call" and ev > 0:
                        signal_type = SignalType.BUY
                    elif option_type == "put" and ev > 0:
                        signal_type = SignalType.BUY
                    else:
                        continue
                    
                    # Calculate confidence based on EV and POP
                    confidence = min(0.95, (ev / option_price + pop / 100) / 2)
                    
                    signal = OptionsSignal(
                        symbol=symbol,
                        signal_type=signal_type,
                        timestamp=datetime.now(),
                        price=option_price,
                        confidence=confidence,
                        strike=strike,
                        expiry_date=expiry_date,
                        option_type=option_type,
                        implied_volatility=implied_vol,
                        delta=greeks['delta'],
                        gamma=greeks['gamma'],
                        theta=greeks['theta'],
                        vega=greeks['vega'],
                        expected_value=ev,
                        probability_of_profit=pop,
                        metadata={
                            'strategy': 'options_ev_pop',
                            'dte': dte,
                            'bid': bid,
                            'ask': ask,
                            'spread': ask - bid,
                            'moneyness': spot_price / strike
                        }
                    )
                    
                    signals.append(signal)
                    
            except Exception as e:
                self.logger.error(f"Error analyzing option {option}: {e}")
                continue
        
        # Sort by expected value (descending)
        signals.sort(key=lambda x: x.expected_value, reverse=True)
        
        return signals
