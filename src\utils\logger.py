"""Logging utilities for the trading engine."""

import logging
from typing import Optional


def get_logger(name: Optional[str] = None) -> logging.Logger:
    """Get a logger instance.
    
    Args:
        name: Logger name. If None, uses the calling module's name.
    
    Returns:
        Logger instance configured according to logging.yaml
    """
    if name is None:
        # Get the calling module's name
        import inspect
        frame = inspect.currentframe().f_back
        name = frame.f_globals.get('__name__', 'trading_engine')
    
    return logging.getLogger(name)
