"""Portfolio monitoring and risk metrics calculation."""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import numpy as np

from ..broker import Position
from ..utils import get_logger, config, calculate_sharpe_ratio


@dataclass
class PortfolioSnapshot:
    """Portfolio snapshot at a point in time."""
    timestamp: datetime
    total_value: float
    cash_balance: float
    positions_value: float
    unrealized_pnl: float
    realized_pnl: float
    daily_pnl: float
    positions_count: int
    largest_position_pct: float
    sector_exposure: Dict[str, float]


class PortfolioMonitor:
    """Monitor portfolio risk metrics and limits."""
    
    def __init__(self):
        self.logger = get_logger("trading_engine.risk.portfolio_monitor")
        
        # Portfolio history
        self.snapshots: List[PortfolioSnapshot] = []
        self.daily_returns: List[float] = []
        
        # Risk limits
        self.max_daily_drawdown = config.max_daily_drawdown  # 3%
        self.max_position_size = config.max_position_size    # 2%
        self.max_concurrent_notional = config.max_concurrent_notional  # 10%
        
        # Tracking
        self.start_of_day_value: Optional[float] = None
        self.peak_value: float = 0.0
        self.max_drawdown: float = 0.0
        self.last_update: Optional[datetime] = None
        
        # Alerts
        self.alerts: List[Dict[str, Any]] = []
    
    def update_portfolio(
        self,
        total_value: float,
        cash_balance: float,
        positions: Dict[str, Position],
        timestamp: Optional[datetime] = None
    ) -> PortfolioSnapshot:
        """Update portfolio monitoring with current state.
        
        Args:
            total_value: Total portfolio value
            cash_balance: Available cash
            positions: Current positions
            timestamp: Update timestamp
            
        Returns:
            Portfolio snapshot
        """
        if timestamp is None:
            timestamp = datetime.now()
        
        # Calculate metrics
        positions_value = sum(pos.market_value for pos in positions.values())
        unrealized_pnl = sum(pos.unrealized_pnl for pos in positions.values())
        realized_pnl = sum(pos.realized_pnl for pos in positions.values())
        
        # Daily P&L calculation
        if self.start_of_day_value is None:
            self.start_of_day_value = total_value
            daily_pnl = 0.0
        else:
            daily_pnl = total_value - self.start_of_day_value
        
        # Check if new day
        if (self.last_update and 
            timestamp.date() != self.last_update.date()):
            self._process_end_of_day(total_value)
        
        # Position analysis
        positions_count = len([pos for pos in positions.values() if pos.quantity != 0])
        largest_position_pct = self._calculate_largest_position_pct(positions, total_value)
        
        # Sector exposure (simplified - would need symbol-to-sector mapping)
        sector_exposure = self._calculate_sector_exposure(positions, total_value)
        
        # Create snapshot
        snapshot = PortfolioSnapshot(
            timestamp=timestamp,
            total_value=total_value,
            cash_balance=cash_balance,
            positions_value=positions_value,
            unrealized_pnl=unrealized_pnl,
            realized_pnl=realized_pnl,
            daily_pnl=daily_pnl,
            positions_count=positions_count,
            largest_position_pct=largest_position_pct,
            sector_exposure=sector_exposure
        )
        
        # Add to history
        self.snapshots.append(snapshot)
        
        # Keep only recent snapshots
        max_snapshots = 10000
        if len(self.snapshots) > max_snapshots:
            self.snapshots = self.snapshots[-max_snapshots:]
        
        # Update tracking
        self.peak_value = max(self.peak_value, total_value)
        self._update_drawdown(total_value)
        self.last_update = timestamp
        
        # Check risk limits
        self._check_risk_limits(snapshot, positions)
        
        return snapshot
    
    def _process_end_of_day(self, current_value: float):
        """Process end of day calculations."""
        if self.start_of_day_value and self.start_of_day_value > 0:
            daily_return = (current_value / self.start_of_day_value) - 1
            self.daily_returns.append(daily_return)
            
            # Keep only recent returns
            max_returns = 252  # 1 year of trading days
            if len(self.daily_returns) > max_returns:
                self.daily_returns = self.daily_returns[-max_returns:]
        
        # Reset for new day
        self.start_of_day_value = current_value
    
    def _calculate_largest_position_pct(
        self,
        positions: Dict[str, Position],
        total_value: float
    ) -> float:
        """Calculate largest position as percentage of portfolio."""
        if total_value <= 0:
            return 0.0
        
        largest_value = 0.0
        for position in positions.values():
            position_value = abs(position.market_value)
            largest_value = max(largest_value, position_value)
        
        return (largest_value / total_value) * 100
    
    def _calculate_sector_exposure(
        self,
        positions: Dict[str, Position],
        total_value: float
    ) -> Dict[str, float]:
        """Calculate sector exposure (simplified implementation)."""
        # This would require a symbol-to-sector mapping
        # For now, return a simple categorization
        sector_exposure = {}
        
        for symbol, position in positions.items():
            if position.quantity == 0:
                continue
            
            # Simplified sector assignment based on symbol
            sector = self._get_symbol_sector(symbol)
            position_pct = (abs(position.market_value) / total_value) * 100 if total_value > 0 else 0
            
            if sector in sector_exposure:
                sector_exposure[sector] += position_pct
            else:
                sector_exposure[sector] = position_pct
        
        return sector_exposure
    
    def _get_symbol_sector(self, symbol: str) -> str:
        """Get sector for symbol (simplified implementation)."""
        # This is a simplified mapping - in production, use a proper sector database
        tech_symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'NVDA', 'META']
        finance_symbols = ['JPM', 'BAC', 'WFC', 'GS', 'MS']
        
        if symbol in tech_symbols:
            return 'Technology'
        elif symbol in finance_symbols:
            return 'Finance'
        elif symbol in ['SPY', 'QQQ', 'IWM']:
            return 'ETF'
        else:
            return 'Other'
    
    def _update_drawdown(self, current_value: float):
        """Update maximum drawdown calculation."""
        if self.peak_value > 0:
            current_drawdown = (self.peak_value - current_value) / self.peak_value
            self.max_drawdown = max(self.max_drawdown, current_drawdown)
    
    def _check_risk_limits(self, snapshot: PortfolioSnapshot, positions: Dict[str, Position]):
        """Check portfolio against risk limits and generate alerts."""
        alerts = []
        
        # Daily drawdown check
        if snapshot.total_value > 0 and self.start_of_day_value:
            daily_drawdown = (self.start_of_day_value - snapshot.total_value) / self.start_of_day_value
            if daily_drawdown > self.max_daily_drawdown:
                alerts.append({
                    'type': 'daily_drawdown_exceeded',
                    'message': f'Daily drawdown {daily_drawdown:.2%} exceeds limit {self.max_daily_drawdown:.2%}',
                    'severity': 'high',
                    'timestamp': snapshot.timestamp,
                    'value': daily_drawdown
                })
        
        # Position size check
        if snapshot.largest_position_pct > self.max_position_size * 100:
            alerts.append({
                'type': 'position_size_exceeded',
                'message': f'Largest position {snapshot.largest_position_pct:.1f}% exceeds limit {self.max_position_size*100:.1f}%',
                'severity': 'medium',
                'timestamp': snapshot.timestamp,
                'value': snapshot.largest_position_pct
            })
        
        # Concurrent notional exposure check
        total_notional = sum(abs(pos.market_value) for pos in positions.values())
        notional_pct = (total_notional / snapshot.total_value) * 100 if snapshot.total_value > 0 else 0
        
        if notional_pct > self.max_concurrent_notional * 100:
            alerts.append({
                'type': 'notional_exposure_exceeded',
                'message': f'Total notional exposure {notional_pct:.1f}% exceeds limit {self.max_concurrent_notional*100:.1f}%',
                'severity': 'high',
                'timestamp': snapshot.timestamp,
                'value': notional_pct
            })
        
        # Add alerts
        for alert in alerts:
            self.alerts.append(alert)
            self.logger.warning(f"Risk Alert: {alert['message']}")
        
        # Keep only recent alerts
        max_alerts = 100
        if len(self.alerts) > max_alerts:
            self.alerts = self.alerts[-max_alerts:]
    
    def get_risk_metrics(self) -> Dict[str, Any]:
        """Get current risk metrics.
        
        Returns:
            Dictionary with risk metrics
        """
        if not self.snapshots:
            return {}
        
        latest = self.snapshots[-1]
        
        # Calculate volatility
        volatility = np.std(self.daily_returns) * np.sqrt(252) if len(self.daily_returns) > 1 else 0
        
        # Calculate Sharpe ratio
        sharpe_ratio = calculate_sharpe_ratio(self.daily_returns) if len(self.daily_returns) > 1 else 0
        
        # Calculate win rate (simplified)
        winning_days = sum(1 for r in self.daily_returns if r > 0)
        win_rate = (winning_days / len(self.daily_returns)) * 100 if self.daily_returns else 0
        
        return {
            'total_value': latest.total_value,
            'daily_pnl': latest.daily_pnl,
            'daily_pnl_pct': (latest.daily_pnl / self.start_of_day_value * 100) if self.start_of_day_value else 0,
            'unrealized_pnl': latest.unrealized_pnl,
            'realized_pnl': latest.realized_pnl,
            'max_drawdown_pct': self.max_drawdown * 100,
            'largest_position_pct': latest.largest_position_pct,
            'positions_count': latest.positions_count,
            'volatility_annualized': volatility * 100,
            'sharpe_ratio': sharpe_ratio,
            'win_rate_pct': win_rate,
            'sector_exposure': latest.sector_exposure,
            'recent_alerts': len([a for a in self.alerts if a['timestamp'] > datetime.now() - timedelta(hours=24)])
        }
    
    def get_recent_alerts(self, hours: int = 24) -> List[Dict[str, Any]]:
        """Get recent alerts.
        
        Args:
            hours: Number of hours to look back
            
        Returns:
            List of recent alerts
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [alert for alert in self.alerts if alert['timestamp'] > cutoff_time]
    
    def is_risk_limit_breached(self) -> bool:
        """Check if any risk limits are currently breached.
        
        Returns:
            True if any risk limits are breached
        """
        recent_alerts = self.get_recent_alerts(hours=1)
        high_severity_alerts = [a for a in recent_alerts if a['severity'] == 'high']
        return len(high_severity_alerts) > 0
    
    def reset_daily_tracking(self):
        """Reset daily tracking (call at start of new trading day)."""
        if self.snapshots:
            self.start_of_day_value = self.snapshots[-1].total_value
        self.logger.info("Reset daily tracking for new trading day")
