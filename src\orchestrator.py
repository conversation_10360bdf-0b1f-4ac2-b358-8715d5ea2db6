"""Main orchestrator for the trading engine."""

import time
import signal
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import threading

from .broker import <PERSON><PERSON>roker, PaperBroker, BaseBroker
from .data_feed import LiveFeed, HistoricalFeed, DataFeedManager, MarketData
from .strategies import StrategyEngine, Signal
from .risk import RiskManager
from .utils import get_logger, config


class TradingOrchestrator:
    """Main orchestrator that coordinates all trading engine components."""
    
    def __init__(self, symbols: List[str], mode: str = None):
        """Initialize the trading orchestrator.
        
        Args:
            symbols: List of symbols to trade
            mode: Trading mode ('live' or 'paper'), defaults to config setting
        """
        self.logger = get_logger("trading_engine.orchestrator")
        self.symbols = symbols
        self.mode = mode or config.mode
        
        # Core components
        self.broker: Optional[BaseBroker] = None
        self.data_feed_manager = DataFeedManager()
        self.strategy_engine = StrategyEngine()
        self.risk_manager: Optional[RiskManager] = None
        
        # State management
        self.is_running = False
        self.is_market_hours = True
        self.last_update: Optional[datetime] = None
        self.update_interval = 60  # seconds
        
        # Performance tracking
        self.total_signals_processed = 0
        self.total_orders_placed = 0
        self.total_risk_stops = 0
        self.start_time: Optional[datetime] = None
        
        # Threading
        self._stop_event = threading.Event()
        self._main_thread: Optional[threading.Thread] = None
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        self.logger.info(f"Initialized trading orchestrator in {self.mode} mode for {len(symbols)} symbols")
    
    def initialize(self) -> bool:
        """Initialize all components.
        
        Returns:
            True if initialization successful
        """
        try:
            self.logger.info("Initializing trading engine components...")
            
            # Initialize broker
            if not self._initialize_broker():
                return False
            
            # Initialize data feeds
            if not self._initialize_data_feeds():
                return False
            
            # Initialize strategies
            if not self._initialize_strategies():
                return False
            
            # Initialize risk management
            if not self._initialize_risk_management():
                return False
            
            self.logger.info("All components initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error during initialization: {e}")
            return False
    
    def _initialize_broker(self) -> bool:
        """Initialize the broker component."""
        try:
            if config.is_live_mode():
                self.broker = LiveBroker()
                self.logger.info("Initialized live broker")
            else:
                self.broker = PaperBroker()
                self.logger.info("Initialized paper broker")
            
            # Connect to broker
            if not self.broker.connect():
                self.logger.error("Failed to connect to broker")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error initializing broker: {e}")
            return False
    
    def _initialize_data_feeds(self) -> bool:
        """Initialize data feed components."""
        try:
            if config.is_live_mode():
                # Live data feed
                live_feed = LiveFeed(self.symbols, update_interval=self.update_interval)
                self.data_feed_manager.add_feed("live", live_feed, is_primary=True)
                self.logger.info("Initialized live data feed")
            else:
                # Historical data feed for backtesting
                # Try to load historical data
                from .data_feed.data_downloader import DataDownloader
                downloader = DataDownloader()
                
                # Check if we have historical data files
                data_files = downloader.list_data_files()
                if data_files:
                    # Use the most recent data file
                    latest_file = sorted(data_files)[-1]
                    self.logger.info(f"Loading historical data from {latest_file}")
                    
                    historical_data = downloader.load_data_file(latest_file)
                    historical_feed = HistoricalFeed(
                        self.symbols,
                        historical_data,
                        start_date=datetime.strptime(config.backtest_start_date, '%Y-%m-%d'),
                        end_date=datetime.strptime(config.backtest_end_date, '%Y-%m-%d')
                    )
                else:
                    # Generate synthetic data for testing
                    self.logger.warning("No historical data found, generating synthetic data")
                    synthetic_data = downloader.generate_synthetic_data(
                        self.symbols,
                        start_date=datetime.strptime(config.backtest_start_date, '%Y-%m-%d'),
                        end_date=datetime.strptime(config.backtest_end_date, '%Y-%m-%d')
                    )
                    historical_feed = HistoricalFeed(self.symbols, synthetic_data)
                
                self.data_feed_manager.add_feed("historical", historical_feed, is_primary=True)
                self.logger.info("Initialized historical data feed")
            
            # Start data feeds
            self.data_feed_manager.start_all()
            return True
            
        except Exception as e:
            self.logger.error(f"Error initializing data feeds: {e}")
            return False
    
    def _initialize_strategies(self) -> bool:
        """Initialize trading strategies."""
        try:
            # Create default strategies
            strategies = self.strategy_engine.create_default_strategies(self.symbols)
            
            self.logger.info(f"Initialized {len(strategies)} trading strategies")
            return True
            
        except Exception as e:
            self.logger.error(f"Error initializing strategies: {e}")
            return False
    
    def _initialize_risk_management(self) -> bool:
        """Initialize risk management."""
        try:
            self.risk_manager = RiskManager(self.broker)
            self.logger.info("Initialized risk management")
            return True
            
        except Exception as e:
            self.logger.error(f"Error initializing risk management: {e}")
            return False
    
    def start(self) -> bool:
        """Start the trading engine.
        
        Returns:
            True if started successfully
        """
        if self.is_running:
            self.logger.warning("Trading engine is already running")
            return False
        
        if not self.initialize():
            self.logger.error("Failed to initialize trading engine")
            return False
        
        self.is_running = True
        self.start_time = datetime.now()
        self._stop_event.clear()
        
        # Start main trading loop in separate thread
        self._main_thread = threading.Thread(target=self._main_loop, daemon=True)
        self._main_thread.start()
        
        self.logger.info("Trading engine started successfully")
        return True
    
    def stop(self):
        """Stop the trading engine."""
        if not self.is_running:
            return
        
        self.logger.info("Stopping trading engine...")
        
        self.is_running = False
        self._stop_event.set()
        
        # Wait for main thread to finish
        if self._main_thread and self._main_thread.is_alive():
            self._main_thread.join(timeout=10)
        
        # Stop data feeds
        self.data_feed_manager.stop_all()
        
        # Disconnect broker
        if self.broker:
            self.broker.disconnect()
        
        self.logger.info("Trading engine stopped")
    
    def _main_loop(self):
        """Main trading loop."""
        self.logger.info("Starting main trading loop")
        
        primary_feed = self.data_feed_manager.get_primary_feed()
        if not primary_feed:
            self.logger.error("No primary data feed available")
            return
        
        try:
            # Stream market data and process
            for market_data in primary_feed.stream():
                if self._stop_event.is_set() or not self.is_running:
                    break
                
                self._process_market_data(market_data)
                
                # Sleep between updates for live mode
                if config.is_live_mode():
                    time.sleep(self.update_interval)
        
        except Exception as e:
            self.logger.error(f"Error in main trading loop: {e}")
        
        finally:
            self.logger.info("Main trading loop ended")
    
    def _process_market_data(self, market_data: MarketData):
        """Process new market data through the trading pipeline.
        
        Args:
            market_data: New market data to process
        """
        try:
            timestamp = market_data.timestamp
            self.logger.debug(f"Processing market data for {timestamp}")
            
            # 1. Execute strategies and get signals
            signals = self.strategy_engine.execute_strategies(market_data)
            self.total_signals_processed += len(signals)
            
            if signals:
                self.logger.info(f"Generated {len(signals)} signals")
            
            # 2. Process signals through risk management
            for signal in signals:
                self._process_signal(signal, market_data)
            
            # 3. Update risk monitoring and get risk control orders
            market_data_dict = {
                symbol: ohlcv.to_dict() for symbol, ohlcv in market_data.data.items()
            }
            
            risk_orders = self.risk_manager.update_risk_monitoring(
                market_data_dict, timestamp
            )
            
            # 4. Execute risk control orders
            for order in risk_orders:
                self._execute_order(order, "risk_control")
                self.total_risk_stops += 1
            
            self.last_update = timestamp
            
        except Exception as e:
            self.logger.error(f"Error processing market data: {e}")
    
    def _process_signal(self, signal: Signal, market_data: MarketData):
        """Process a trading signal.
        
        Args:
            signal: Trading signal to process
            market_data: Current market data
        """
        try:
            # Get market data for the signal's symbol
            symbol_data = market_data.get_symbol_data(signal.symbol)
            if not symbol_data:
                self.logger.warning(f"No market data for signal symbol {signal.symbol}")
                return
            
            # Convert to dict format for risk manager
            market_data_dict = symbol_data.to_dict()
            
            # Evaluate signal through risk management
            should_execute, position_size, reason = self.risk_manager.evaluate_signal(
                signal, market_data_dict
            )
            
            if should_execute and position_size:
                # Update signal with calculated position size
                signal.quantity = position_size
                
                # Create and execute order
                order_args = signal.to_order_args()
                order = self.broker.place_order(**order_args)
                
                if order and order.status.value not in ['rejected', 'failed']:
                    self.total_orders_placed += 1
                    self.logger.info(f"Executed signal: {signal.signal_type.value} {position_size} {signal.symbol}")
                    
                    # Add risk controls for new position (if order is filled)
                    if order.is_filled:
                        position = self.broker.get_position(signal.symbol)
                        if position:
                            self.risk_manager.add_position_risk_controls(
                                signal.symbol, position, signal
                            )
                else:
                    self.logger.warning(f"Failed to execute signal for {signal.symbol}: {order.status if order else 'No order returned'}")
            else:
                self.logger.debug(f"Signal rejected for {signal.symbol}: {reason}")
                
        except Exception as e:
            self.logger.error(f"Error processing signal for {signal.symbol}: {e}")
    
    def _execute_order(self, order, order_type: str = "strategy"):
        """Execute an order.
        
        Args:
            order: Order to execute
            order_type: Type of order (strategy, risk_control, etc.)
        """
        try:
            executed_order = self.broker.place_order(
                symbol=order.symbol,
                side=order.side,
                order_type=order.order_type,
                quantity=order.quantity,
                price=order.price
            )
            
            if executed_order and executed_order.status.value not in ['rejected', 'failed']:
                self.logger.info(f"Executed {order_type} order: {order.side.value} {order.quantity} {order.symbol}")
            else:
                self.logger.warning(f"Failed to execute {order_type} order for {order.symbol}")
                
        except Exception as e:
            self.logger.error(f"Error executing {order_type} order: {e}")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        self.logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.stop()
        sys.exit(0)
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status of the trading engine.
        
        Returns:
            Dictionary with status information
        """
        uptime = datetime.now() - self.start_time if self.start_time else timedelta(0)
        
        status = {
            'is_running': self.is_running,
            'mode': self.mode,
            'symbols': self.symbols,
            'uptime_seconds': uptime.total_seconds(),
            'last_update': self.last_update.isoformat() if self.last_update else None,
            'total_signals_processed': self.total_signals_processed,
            'total_orders_placed': self.total_orders_placed,
            'total_risk_stops': self.total_risk_stops
        }
        
        # Add component status
        if self.broker:
            status['broker_connected'] = self.broker.is_connected()
            status['portfolio_value'] = self.broker.get_portfolio_value()
            status['cash_balance'] = self.broker.get_cash_balance()
        
        if self.risk_manager:
            status['risk_status'] = self.risk_manager.get_risk_status()
        
        if self.strategy_engine:
            status['strategy_stats'] = self.strategy_engine.get_performance_stats()
        
        return status
    
    def run_backtest(self) -> Dict[str, Any]:
        """Run a backtest and return results.
        
        Returns:
            Dictionary with backtest results
        """
        if config.is_live_mode():
            raise ValueError("Cannot run backtest in live mode")
        
        self.logger.info("Starting backtest...")
        
        if not self.start():
            raise RuntimeError("Failed to start trading engine for backtest")
        
        # Wait for backtest to complete
        while self.is_running and self._main_thread and self._main_thread.is_alive():
            time.sleep(1)
        
        # Get final results
        if hasattr(self.broker, 'get_performance_stats'):
            performance_stats = self.broker.get_performance_stats()
        else:
            performance_stats = {}
        
        results = {
            'status': self.get_status(),
            'performance': performance_stats,
            'final_portfolio_value': self.broker.get_portfolio_value() if self.broker else 0
        }
        
        self.logger.info("Backtest completed")
        return results


def main():
    """Main entry point for the trading engine."""
    # Default symbols for testing
    symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'SPY']
    
    # Create and start orchestrator
    orchestrator = TradingOrchestrator(symbols)
    
    try:
        if config.is_paper_mode():
            # Run backtest
            results = orchestrator.run_backtest()
            print(f"Backtest completed. Final portfolio value: ${results['final_portfolio_value']:,.2f}")
        else:
            # Run live trading
            if orchestrator.start():
                print("Trading engine started. Press Ctrl+C to stop.")
                
                # Keep running until interrupted
                while orchestrator.is_running:
                    time.sleep(1)
            else:
                print("Failed to start trading engine")
                
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        orchestrator.stop()


if __name__ == "__main__":
    main()
