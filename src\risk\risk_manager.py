"""Main risk manager coordinating all risk management components."""

from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
import threading

from .position_sizer import PositionS<PERSON>, SizingMethod
from .stop_loss_manager import StopLossManager, StopType
from .portfolio_monitor import PortfolioMonitor
from ..broker import BaseBroker, Position, Order, OrderSide
from ..strategies import Signal, SignalType
from ..utils import get_logger, config


class RiskManager:
    """Main risk manager for the trading engine."""
    
    def __init__(self, broker: BaseBroker):
        self.logger = get_logger("trading_engine.risk_manager")
        self.broker = broker
        
        # Risk components
        self.position_sizer = PositionSizer()
        self.stop_loss_manager = StopLossManager()
        self.portfolio_monitor = PortfolioMonitor()
        
        # Configuration
        self.default_sizing_method = SizingMethod.FIXED_PERCENT
        self.default_stop_type = StopType.ATR_BASED
        self.emergency_stop_enabled = True
        
        # State tracking
        self.is_emergency_stop = False
        self.last_update: Optional[datetime] = None
        self._lock = threading.Lock()
    
    def evaluate_signal(
        self,
        signal: Signal,
        market_data: Dict[str, Any],
        atr_value: Optional[float] = None
    ) -> Tuple[bool, Optional[int], str]:
        """Evaluate if a signal should be executed and determine position size.
        
        Args:
            signal: Trading signal to evaluate
            market_data: Current market data
            atr_value: ATR value for the symbol
            
        Returns:
            Tuple of (should_execute, position_size, reason)
        """
        with self._lock:
            try:
                # Check emergency stop
                if self.is_emergency_stop:
                    return False, None, "Emergency stop is active"
                
                # Check portfolio risk limits
                if self.portfolio_monitor.is_risk_limit_breached():
                    return False, None, "Portfolio risk limits breached"
                
                # Get current portfolio state
                account_value = self.broker.get_portfolio_value()
                positions = self.broker.get_positions()
                
                # Check if we already have a position in this symbol
                existing_position = positions.get(signal.symbol)
                if existing_position and existing_position.quantity != 0:
                    # Already have a position - check if signal is for closing
                    if self._is_closing_signal(signal, existing_position):
                        return True, abs(existing_position.quantity), "Closing existing position"
                    else:
                        return False, None, f"Already have position in {signal.symbol}"
                
                # Calculate position size
                position_size = self._calculate_position_size(
                    signal, account_value, market_data, atr_value
                )
                
                if position_size <= 0:
                    return False, None, "Position size calculation resulted in zero shares"
                
                # Validate position against risk limits
                is_valid, reason = self._validate_position_risk(
                    signal, position_size, account_value, positions
                )
                
                if not is_valid:
                    return False, None, reason
                
                return True, position_size, "Signal approved"
                
            except Exception as e:
                self.logger.error(f"Error evaluating signal for {signal.symbol}: {e}")
                return False, None, f"Error in risk evaluation: {e}"
    
    def _is_closing_signal(self, signal: Signal, position: Position) -> bool:
        """Check if signal is for closing an existing position."""
        is_long_position = position.quantity > 0
        is_sell_signal = signal.signal_type in [SignalType.SELL, SignalType.CLOSE]
        is_short_position = position.quantity < 0
        is_buy_signal = signal.signal_type in [SignalType.BUY, SignalType.CLOSE]
        
        return (is_long_position and is_sell_signal) or (is_short_position and is_buy_signal)
    
    def _calculate_position_size(
        self,
        signal: Signal,
        account_value: float,
        market_data: Dict[str, Any],
        atr_value: Optional[float]
    ) -> int:
        """Calculate position size for a signal."""
        # Use signal's suggested quantity if provided
        if signal.quantity and signal.quantity > 0:
            return signal.quantity
        
        # Calculate stop loss price
        stop_loss_price = signal.stop_loss
        if not stop_loss_price:
            # Calculate default stop loss
            if atr_value:
                atr_multiplier = 2.0
                if signal.signal_type == SignalType.BUY:
                    stop_loss_price = signal.price - (atr_value * atr_multiplier)
                else:
                    stop_loss_price = signal.price + (atr_value * atr_multiplier)
            else:
                # Use percentage stop
                stop_percent = 0.05  # 5%
                if signal.signal_type == SignalType.BUY:
                    stop_loss_price = signal.price * (1 - stop_percent)
                else:
                    stop_loss_price = signal.price * (1 + stop_percent)
        
        # Calculate position size
        position_size = self.position_sizer.calculate_position_size(
            account_value=account_value,
            entry_price=signal.price,
            stop_loss_price=stop_loss_price,
            method=self.default_sizing_method,
            confidence=signal.confidence,
            atr_value=atr_value
        )
        
        return position_size
    
    def _validate_position_risk(
        self,
        signal: Signal,
        position_size: int,
        account_value: float,
        positions: Dict[str, Position]
    ) -> Tuple[bool, str]:
        """Validate position against risk limits."""
        
        # Check position value limit
        position_value = position_size * signal.price
        max_position_value = account_value * config.max_position_size
        
        if position_value > max_position_value:
            return False, f"Position value ${position_value:.2f} exceeds limit ${max_position_value:.2f}"
        
        # Check total notional exposure
        current_notional = sum(abs(pos.market_value) for pos in positions.values())
        new_notional = current_notional + position_value
        max_notional = account_value * config.max_concurrent_notional
        
        if new_notional > max_notional:
            return False, f"Total notional ${new_notional:.2f} would exceed limit ${max_notional:.2f}"
        
        # Check buying power
        buying_power = self.broker.get_buying_power()
        if position_value > buying_power:
            return False, f"Insufficient buying power: need ${position_value:.2f}, have ${buying_power:.2f}"
        
        return True, "Position risk validated"
    
    def add_position_risk_controls(
        self,
        symbol: str,
        position: Position,
        signal: Signal,
        atr_value: Optional[float] = None
    ) -> bool:
        """Add risk controls for a new position.
        
        Args:
            symbol: Symbol of the position
            position: Position object
            signal: Original signal that created the position
            atr_value: ATR value for stop loss calculation
            
        Returns:
            True if risk controls were added successfully
        """
        try:
            # Add stop loss
            stop_success = self.stop_loss_manager.add_stop_loss(
                symbol=symbol,
                position=position,
                stop_type=self.default_stop_type,
                stop_price=signal.stop_loss,
                atr_value=atr_value
            )
            
            if stop_success:
                self.logger.info(f"Added risk controls for {symbol}")
                return True
            else:
                self.logger.warning(f"Failed to add stop loss for {symbol}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error adding risk controls for {symbol}: {e}")
            return False
    
    def update_risk_monitoring(
        self,
        market_data: Dict[str, Dict[str, Any]],
        timestamp: Optional[datetime] = None
    ) -> List[Order]:
        """Update risk monitoring and return any risk control orders.
        
        Args:
            market_data: Current market data
            timestamp: Update timestamp
            
        Returns:
            List of orders to execute for risk control
        """
        with self._lock:
            orders_to_execute = []
            
            try:
                # Get current portfolio state
                total_value = self.broker.get_portfolio_value()
                cash_balance = self.broker.get_cash_balance()
                positions = self.broker.get_positions()
                
                # Update portfolio monitoring
                snapshot = self.portfolio_monitor.update_portfolio(
                    total_value=total_value,
                    cash_balance=cash_balance,
                    positions=positions,
                    timestamp=timestamp
                )
                
                # Update stop losses and get stop orders
                stop_orders = self.stop_loss_manager.update_stops(positions, market_data)
                orders_to_execute.extend(stop_orders)
                
                # Check for emergency stop conditions
                self._check_emergency_stop(snapshot)
                
                self.last_update = timestamp or datetime.now()
                
            except Exception as e:
                self.logger.error(f"Error updating risk monitoring: {e}")
            
            return orders_to_execute
    
    def _check_emergency_stop(self, snapshot):
        """Check if emergency stop should be triggered."""
        # Check daily drawdown
        if (snapshot.total_value > 0 and 
            self.portfolio_monitor.start_of_day_value and
            self.portfolio_monitor.start_of_day_value > 0):
            
            daily_drawdown = ((self.portfolio_monitor.start_of_day_value - snapshot.total_value) / 
                            self.portfolio_monitor.start_of_day_value)
            
            # Trigger emergency stop at 1.5x the daily limit
            emergency_threshold = config.max_daily_drawdown * 1.5
            
            if daily_drawdown > emergency_threshold and not self.is_emergency_stop:
                self.trigger_emergency_stop(f"Daily drawdown {daily_drawdown:.2%} exceeds emergency threshold")
    
    def trigger_emergency_stop(self, reason: str):
        """Trigger emergency stop to halt all trading.
        
        Args:
            reason: Reason for emergency stop
        """
        self.is_emergency_stop = True
        self.logger.critical(f"EMERGENCY STOP TRIGGERED: {reason}")
        
        # Log alert
        alert = {
            'type': 'emergency_stop',
            'message': f'Emergency stop triggered: {reason}',
            'severity': 'critical',
            'timestamp': datetime.now()
        }
        self.portfolio_monitor.alerts.append(alert)
    
    def clear_emergency_stop(self, reason: str = "Manual override"):
        """Clear emergency stop condition.
        
        Args:
            reason: Reason for clearing emergency stop
        """
        self.is_emergency_stop = False
        self.logger.warning(f"Emergency stop cleared: {reason}")
    
    def get_risk_status(self) -> Dict[str, Any]:
        """Get current risk status summary.
        
        Returns:
            Dictionary with risk status information
        """
        risk_metrics = self.portfolio_monitor.get_risk_metrics()
        recent_alerts = self.portfolio_monitor.get_recent_alerts()
        
        return {
            'emergency_stop_active': self.is_emergency_stop,
            'risk_limits_breached': self.portfolio_monitor.is_risk_limit_breached(),
            'last_update': self.last_update.isoformat() if self.last_update else None,
            'portfolio_metrics': risk_metrics,
            'recent_alerts_count': len(recent_alerts),
            'active_stops_count': len(self.stop_loss_manager.stop_orders),
            'high_severity_alerts': len([a for a in recent_alerts if a['severity'] == 'high'])
        }
    
    def get_position_risk_info(self, symbol: str) -> Dict[str, Any]:
        """Get risk information for a specific position.
        
        Args:
            symbol: Symbol to get risk info for
            
        Returns:
            Dictionary with position risk information
        """
        stop_info = self.stop_loss_manager.get_stop_info(symbol)
        position = self.broker.get_position(symbol)
        
        info = {
            'symbol': symbol,
            'has_position': position is not None and position.quantity != 0,
            'has_stop_loss': stop_info is not None,
            'stop_loss_info': stop_info
        }
        
        if position:
            account_value = self.broker.get_portfolio_value()
            position_pct = (abs(position.market_value) / account_value * 100) if account_value > 0 else 0
            
            info.update({
                'position_value': position.market_value,
                'position_pct': position_pct,
                'unrealized_pnl': position.unrealized_pnl,
                'quantity': position.quantity
            })
        
        return info
