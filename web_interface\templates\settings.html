{% extends "base.html" %}

{% block title %}Settings - Robinhood Trading Engine{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-cog me-2"></i>Trading Engine Settings</h2>
        <p class="text-muted">Configure your trading engine parameters and risk management settings.</p>
    </div>
</div>

<!-- Engine Configuration -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-sliders-h me-2"></i>Engine Configuration</h5>
            </div>
            <div class="card-body">
                <form id="engine-config-form">
                    <div class="mb-3">
                        <label for="trading-mode" class="form-label">Trading Mode</label>
                        <select class="form-select" id="trading-mode">
                            <option value="paper">Paper Trading (Simulation)</option>
                            <option value="live">Live Trading (Real Money)</option>
                        </select>
                        <div class="form-text">⚠️ Live trading uses real money. Start with paper trading.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="initial-capital" class="form-label">Initial Capital</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input type="number" class="form-control" id="initial-capital" value="10000" min="1000" step="100">
                        </div>
                        <div class="form-text">Starting capital for paper trading or account value for live trading.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="symbols-input" class="form-label">Trading Symbols</label>
                        <input type="text" class="form-control" id="symbols-input" 
                               value="AAPL,GOOGL,MSFT,TSLA,SPY" 
                               placeholder="Enter symbols separated by commas">
                        <div class="form-text">Stocks to trade (comma-separated, e.g., AAPL,GOOGL,MSFT).</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="update-interval" class="form-label">Update Interval (seconds)</label>
                        <input type="number" class="form-control" id="update-interval" value="60" min="1" max="300">
                        <div class="form-text">How often to check for new market data and signals.</div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Risk Management</h5>
            </div>
            <div class="card-body">
                <form id="risk-config-form">
                    <div class="mb-3">
                        <label for="max-position-size" class="form-label">Max Position Size (%)</label>
                        <input type="number" class="form-control" id="max-position-size" 
                               value="2" min="0.1" max="10" step="0.1">
                        <div class="form-text">Maximum percentage of portfolio per position.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="max-daily-drawdown" class="form-label">Max Daily Drawdown (%)</label>
                        <input type="number" class="form-control" id="max-daily-drawdown" 
                               value="3" min="0.5" max="10" step="0.1">
                        <div class="form-text">Maximum daily loss before emergency stop.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="max-concurrent-notional" class="form-label">Max Concurrent Exposure (%)</label>
                        <input type="number" class="form-control" id="max-concurrent-notional" 
                               value="10" min="1" max="50" step="1">
                        <div class="form-text">Maximum total notional exposure across all positions.</div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="auto-stop-loss" checked>
                            <label class="form-check-label" for="auto-stop-loss">
                                Automatic Stop Loss
                            </label>
                        </div>
                        <div class="form-text">Automatically add stop losses to new positions.</div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Strategy Configuration -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-brain me-2"></i>Strategy Parameters</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6>TTM Squeeze Strategy</h6>
                        <div class="mb-3">
                            <label for="ttm-length" class="form-label">Length</label>
                            <input type="number" class="form-control" id="ttm-length" value="20" min="5" max="50">
                        </div>
                        <div class="mb-3">
                            <label for="ttm-multiplier" class="form-label">Multiplier</label>
                            <input type="number" class="form-control" id="ttm-multiplier" value="2.0" min="1.0" max="3.0" step="0.1">
                        </div>
                        <div class="mb-3">
                            <label for="ttm-min-squeeze" class="form-label">Min Squeeze Periods</label>
                            <input type="number" class="form-control" id="ttm-min-squeeze" value="5" min="1" max="20">
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <h6>EMA Crossover Strategy</h6>
                        <div class="mb-3">
                            <label for="ema-fast" class="form-label">Fast EMA Period</label>
                            <input type="number" class="form-control" id="ema-fast" value="12" min="5" max="50">
                        </div>
                        <div class="mb-3">
                            <label for="ema-slow" class="form-label">Slow EMA Period</label>
                            <input type="number" class="form-control" id="ema-slow" value="26" min="10" max="100">
                        </div>
                        <div class="mb-3">
                            <label for="ema-signal" class="form-label">Signal Period</label>
                            <input type="number" class="form-control" id="ema-signal" value="9" min="5" max="20">
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <h6>ATR Breakout Strategy</h6>
                        <div class="mb-3">
                            <label for="atr-period" class="form-label">ATR Period</label>
                            <input type="number" class="form-control" id="atr-period" value="14" min="5" max="30">
                        </div>
                        <div class="mb-3">
                            <label for="atr-breakout-mult" class="form-label">Breakout Multiplier</label>
                            <input type="number" class="form-control" id="atr-breakout-mult" value="2.0" min="1.0" max="4.0" step="0.1">
                        </div>
                        <div class="mb-3">
                            <label for="atr-stop-mult" class="form-label">Stop Multiplier</label>
                            <input type="number" class="form-control" id="atr-stop-mult" value="1.5" min="0.5" max="3.0" step="0.1">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center">
                <button class="btn btn-primary btn-lg me-3" onclick="saveSettings()">
                    <i class="fas fa-save me-2"></i>Save Settings
                </button>
                <button class="btn btn-secondary btn-lg me-3" onclick="resetToDefaults()">
                    <i class="fas fa-undo me-2"></i>Reset to Defaults
                </button>
                <button class="btn btn-info btn-lg" onclick="exportSettings()">
                    <i class="fas fa-download me-2"></i>Export Configuration
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Import Configuration Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark">
            <div class="modal-header">
                <h5 class="modal-title">Import Configuration</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="config-file" class="form-label">Select Configuration File</label>
                    <input type="file" class="form-control" id="config-file" accept=".json">
                </div>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Importing a configuration will overwrite current settings.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="importConfiguration()">Import</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Load current settings on page load
    function loadInitialData() {
        loadCurrentSettings();
    }

    // Load current settings from the engine
    async function loadCurrentSettings() {
        try {
            const status = await apiRequest('/api/status');
            if (status && !status.error) {
                // Update UI with current settings
                if (status.mode) {
                    document.getElementById('trading-mode').value = status.mode;
                }
            }
        } catch (error) {
            console.error('Error loading current settings:', error);
        }
    }

    // Save settings
    function saveSettings() {
        const settings = {
            trading_mode: document.getElementById('trading-mode').value,
            initial_capital: parseFloat(document.getElementById('initial-capital').value),
            symbols: document.getElementById('symbols-input').value.split(',').map(s => s.trim()),
            update_interval: parseInt(document.getElementById('update-interval').value),
            risk_management: {
                max_position_size: parseFloat(document.getElementById('max-position-size').value) / 100,
                max_daily_drawdown: parseFloat(document.getElementById('max-daily-drawdown').value) / 100,
                max_concurrent_notional: parseFloat(document.getElementById('max-concurrent-notional').value) / 100,
                auto_stop_loss: document.getElementById('auto-stop-loss').checked
            },
            strategies: {
                ttm_squeeze: {
                    length: parseInt(document.getElementById('ttm-length').value),
                    multiplier: parseFloat(document.getElementById('ttm-multiplier').value),
                    min_squeeze_periods: parseInt(document.getElementById('ttm-min-squeeze').value)
                },
                ema_crossover: {
                    fast_period: parseInt(document.getElementById('ema-fast').value),
                    slow_period: parseInt(document.getElementById('ema-slow').value),
                    signal_period: parseInt(document.getElementById('ema-signal').value)
                },
                atr_breakout: {
                    atr_period: parseInt(document.getElementById('atr-period').value),
                    breakout_multiplier: parseFloat(document.getElementById('atr-breakout-mult').value),
                    stop_multiplier: parseFloat(document.getElementById('atr-stop-mult').value)
                }
            }
        };

        // Save to localStorage for now (in production, this would be saved to the server)
        localStorage.setItem('trading_engine_settings', JSON.stringify(settings));
        
        showAlert('Settings Saved', 'Your configuration has been saved successfully.', 'success');
    }

    // Reset to default settings
    function resetToDefaults() {
        if (!confirm('Are you sure you want to reset all settings to defaults? This cannot be undone.')) {
            return;
        }

        // Reset all form fields to default values
        document.getElementById('trading-mode').value = 'paper';
        document.getElementById('initial-capital').value = '10000';
        document.getElementById('symbols-input').value = 'AAPL,GOOGL,MSFT,TSLA,SPY';
        document.getElementById('update-interval').value = '60';
        document.getElementById('max-position-size').value = '2';
        document.getElementById('max-daily-drawdown').value = '3';
        document.getElementById('max-concurrent-notional').value = '10';
        document.getElementById('auto-stop-loss').checked = true;
        document.getElementById('ttm-length').value = '20';
        document.getElementById('ttm-multiplier').value = '2.0';
        document.getElementById('ttm-min-squeeze').value = '5';
        document.getElementById('ema-fast').value = '12';
        document.getElementById('ema-slow').value = '26';
        document.getElementById('ema-signal').value = '9';
        document.getElementById('atr-period').value = '14';
        document.getElementById('atr-breakout-mult').value = '2.0';
        document.getElementById('atr-stop-mult').value = '1.5';

        showAlert('Settings Reset', 'All settings have been reset to defaults.', 'info');
    }

    // Export settings to JSON file
    function exportSettings() {
        const settings = {
            trading_mode: document.getElementById('trading-mode').value,
            initial_capital: parseFloat(document.getElementById('initial-capital').value),
            symbols: document.getElementById('symbols-input').value.split(',').map(s => s.trim()),
            update_interval: parseInt(document.getElementById('update-interval').value),
            risk_management: {
                max_position_size: parseFloat(document.getElementById('max-position-size').value) / 100,
                max_daily_drawdown: parseFloat(document.getElementById('max-daily-drawdown').value) / 100,
                max_concurrent_notional: parseFloat(document.getElementById('max-concurrent-notional').value) / 100,
                auto_stop_loss: document.getElementById('auto-stop-loss').checked
            },
            strategies: {
                ttm_squeeze: {
                    length: parseInt(document.getElementById('ttm-length').value),
                    multiplier: parseFloat(document.getElementById('ttm-multiplier').value),
                    min_squeeze_periods: parseInt(document.getElementById('ttm-min-squeeze').value)
                },
                ema_crossover: {
                    fast_period: parseInt(document.getElementById('ema-fast').value),
                    slow_period: parseInt(document.getElementById('ema-slow').value),
                    signal_period: parseInt(document.getElementById('ema-signal').value)
                },
                atr_breakout: {
                    atr_period: parseInt(document.getElementById('atr-period').value),
                    breakout_multiplier: parseFloat(document.getElementById('atr-breakout-mult').value),
                    stop_multiplier: parseFloat(document.getElementById('atr-stop-mult').value)
                }
            },
            exported_at: new Date().toISOString()
        };

        const blob = new Blob([JSON.stringify(settings, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `trading_engine_config_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        showAlert('Configuration Exported', 'Your configuration has been downloaded as a JSON file.', 'success');
    }

    // Show alert modal
    function showAlert(title, message, type = 'info') {
        // Create a simple alert for now
        alert(`${title}: ${message}`);
    }
</script>
{% endblock %}
