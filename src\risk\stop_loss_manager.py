"""Stop loss management for risk control."""

from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

from ..broker import Position, Order, OrderSide, OrderType
from ..utils import get_logger


class StopType(Enum):
    """Types of stop loss orders."""
    FIXED = "fixed"
    TRAILING = "trailing"
    ATR_BASED = "atr_based"
    TIME_BASED = "time_based"
    VOLATILITY_BASED = "volatility_based"


@dataclass
class StopLossOrder:
    """Stop loss order tracking."""
    symbol: str
    stop_type: StopType
    stop_price: float
    original_stop: float
    position_size: int
    entry_price: float
    entry_time: datetime
    last_update: datetime
    is_active: bool = True
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class StopLossManager:
    """Manager for stop loss orders and risk control."""
    
    def __init__(self):
        self.logger = get_logger("trading_engine.risk.stop_loss")
        
        # Active stop loss orders
        self.stop_orders: Dict[str, StopLossOrder] = {}
        
        # Configuration
        self.default_atr_multiplier = 2.0
        self.trailing_stop_percent = 0.05  # 5%
        self.max_time_in_position = timedelta(days=30)
        self.volatility_lookback = 20
    
    def add_stop_loss(
        self,
        symbol: str,
        position: Position,
        stop_type: StopType,
        stop_price: Optional[float] = None,
        atr_value: Optional[float] = None,
        **kwargs
    ) -> bool:
        """Add a stop loss order for a position.
        
        Args:
            symbol: Symbol to add stop for
            position: Current position
            stop_type: Type of stop loss
            stop_price: Initial stop price (if not calculated)
            atr_value: ATR value for ATR-based stops
            **kwargs: Additional parameters
            
        Returns:
            True if stop was added successfully
        """
        try:
            if symbol in self.stop_orders:
                self.logger.warning(f"Stop loss already exists for {symbol}, updating...")
            
            # Calculate stop price if not provided
            if stop_price is None:
                stop_price = self._calculate_stop_price(
                    position, stop_type, atr_value, **kwargs
                )
            
            if stop_price is None:
                self.logger.error(f"Could not calculate stop price for {symbol}")
                return False
            
            stop_order = StopLossOrder(
                symbol=symbol,
                stop_type=stop_type,
                stop_price=stop_price,
                original_stop=stop_price,
                position_size=position.quantity,
                entry_price=position.average_price,
                entry_time=position.created_at,
                last_update=datetime.now(),
                metadata=kwargs
            )
            
            self.stop_orders[symbol] = stop_order
            
            self.logger.info(f"Added {stop_type.value} stop loss for {symbol} at ${stop_price:.2f}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error adding stop loss for {symbol}: {e}")
            return False
    
    def update_stops(
        self,
        positions: Dict[str, Position],
        market_data: Dict[str, Dict[str, Any]]
    ) -> List[Order]:
        """Update stop loss orders and return orders to execute.
        
        Args:
            positions: Current positions
            market_data: Current market data
            
        Returns:
            List of stop loss orders to execute
        """
        orders_to_execute = []
        
        for symbol, stop_order in list(self.stop_orders.items()):
            if not stop_order.is_active:
                continue
            
            # Check if position still exists
            if symbol not in positions:
                self.logger.info(f"Position closed for {symbol}, removing stop loss")
                del self.stop_orders[symbol]
                continue
            
            position = positions[symbol]
            
            # Get current market data
            if symbol not in market_data:
                continue
            
            current_price = market_data[symbol].get('close', position.market_price)
            
            # Update stop price if needed
            self._update_stop_price(stop_order, current_price, market_data[symbol])
            
            # Check if stop should be triggered
            should_trigger = self._should_trigger_stop(stop_order, current_price, position)
            
            if should_trigger:
                order = self._create_stop_order(stop_order, position)
                if order:
                    orders_to_execute.append(order)
                    stop_order.is_active = False
                    self.logger.info(f"Stop loss triggered for {symbol} at ${current_price:.2f}")
        
        return orders_to_execute
    
    def _calculate_stop_price(
        self,
        position: Position,
        stop_type: StopType,
        atr_value: Optional[float] = None,
        **kwargs
    ) -> Optional[float]:
        """Calculate initial stop price based on stop type."""
        entry_price = position.average_price
        is_long = position.quantity > 0
        
        if stop_type == StopType.FIXED:
            stop_percent = kwargs.get('stop_percent', 0.05)  # 5% default
            if is_long:
                return entry_price * (1 - stop_percent)
            else:
                return entry_price * (1 + stop_percent)
        
        elif stop_type == StopType.ATR_BASED:
            if not atr_value:
                self.logger.warning("ATR value required for ATR-based stop")
                return None
            
            atr_multiplier = kwargs.get('atr_multiplier', self.default_atr_multiplier)
            if is_long:
                return entry_price - (atr_value * atr_multiplier)
            else:
                return entry_price + (atr_value * atr_multiplier)
        
        elif stop_type == StopType.TRAILING:
            trail_percent = kwargs.get('trail_percent', self.trailing_stop_percent)
            current_price = kwargs.get('current_price', entry_price)
            
            if is_long:
                return current_price * (1 - trail_percent)
            else:
                return current_price * (1 + trail_percent)
        
        elif stop_type == StopType.TIME_BASED:
            # Time-based stops use a percentage stop initially
            stop_percent = kwargs.get('stop_percent', 0.10)  # 10% for time stops
            if is_long:
                return entry_price * (1 - stop_percent)
            else:
                return entry_price * (1 + stop_percent)
        
        elif stop_type == StopType.VOLATILITY_BASED:
            volatility = kwargs.get('volatility', 0.02)  # 2% default volatility
            vol_multiplier = kwargs.get('vol_multiplier', 2.0)
            
            if is_long:
                return entry_price * (1 - volatility * vol_multiplier)
            else:
                return entry_price * (1 + volatility * vol_multiplier)
        
        return None
    
    def _update_stop_price(
        self,
        stop_order: StopLossOrder,
        current_price: float,
        market_data: Dict[str, Any]
    ):
        """Update stop price for trailing and dynamic stops."""
        if stop_order.stop_type == StopType.TRAILING:
            self._update_trailing_stop(stop_order, current_price)
        elif stop_order.stop_type == StopType.VOLATILITY_BASED:
            self._update_volatility_stop(stop_order, current_price, market_data)
        
        stop_order.last_update = datetime.now()
    
    def _update_trailing_stop(self, stop_order: StopLossOrder, current_price: float):
        """Update trailing stop price."""
        is_long = stop_order.position_size > 0
        trail_percent = stop_order.metadata.get('trail_percent', self.trailing_stop_percent)
        
        if is_long:
            # For long positions, only move stop up
            new_stop = current_price * (1 - trail_percent)
            if new_stop > stop_order.stop_price:
                stop_order.stop_price = new_stop
        else:
            # For short positions, only move stop down
            new_stop = current_price * (1 + trail_percent)
            if new_stop < stop_order.stop_price:
                stop_order.stop_price = new_stop
    
    def _update_volatility_stop(
        self,
        stop_order: StopLossOrder,
        current_price: float,
        market_data: Dict[str, Any]
    ):
        """Update volatility-based stop price."""
        # This would require historical volatility calculation
        # For now, keep the stop price unchanged
        pass
    
    def _should_trigger_stop(
        self,
        stop_order: StopLossOrder,
        current_price: float,
        position: Position
    ) -> bool:
        """Check if stop loss should be triggered."""
        is_long = position.quantity > 0
        
        # Price-based trigger
        if stop_order.stop_type != StopType.TIME_BASED:
            if is_long and current_price <= stop_order.stop_price:
                return True
            elif not is_long and current_price >= stop_order.stop_price:
                return True
        
        # Time-based trigger
        if stop_order.stop_type == StopType.TIME_BASED:
            time_in_position = datetime.now() - stop_order.entry_time
            max_time = stop_order.metadata.get('max_time', self.max_time_in_position)
            
            if time_in_position >= max_time:
                return True
        
        return False
    
    def _create_stop_order(self, stop_order: StopLossOrder, position: Position) -> Optional[Order]:
        """Create order to close position at stop loss."""
        try:
            # Determine order side (opposite of position)
            side = OrderSide.SELL if position.quantity > 0 else OrderSide.BUY
            
            # Use market order for immediate execution
            order = Order(
                symbol=stop_order.symbol,
                side=side,
                order_type=OrderType.MARKET,
                quantity=abs(position.quantity),
                metadata={
                    'stop_loss': True,
                    'stop_type': stop_order.stop_type.value,
                    'trigger_price': stop_order.stop_price
                }
            )
            
            return order
            
        except Exception as e:
            self.logger.error(f"Error creating stop order for {stop_order.symbol}: {e}")
            return None
    
    def remove_stop(self, symbol: str) -> bool:
        """Remove stop loss for a symbol.
        
        Args:
            symbol: Symbol to remove stop for
            
        Returns:
            True if stop was removed
        """
        if symbol in self.stop_orders:
            del self.stop_orders[symbol]
            self.logger.info(f"Removed stop loss for {symbol}")
            return True
        return False
    
    def get_stop_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get stop loss information for a symbol.
        
        Args:
            symbol: Symbol to get info for
            
        Returns:
            Dictionary with stop loss information
        """
        if symbol not in self.stop_orders:
            return None
        
        stop_order = self.stop_orders[symbol]
        
        return {
            'symbol': stop_order.symbol,
            'stop_type': stop_order.stop_type.value,
            'stop_price': stop_order.stop_price,
            'original_stop': stop_order.original_stop,
            'entry_price': stop_order.entry_price,
            'entry_time': stop_order.entry_time.isoformat(),
            'last_update': stop_order.last_update.isoformat(),
            'is_active': stop_order.is_active,
            'risk_amount': abs(stop_order.entry_price - stop_order.stop_price) * stop_order.position_size
        }
    
    def get_all_stops(self) -> Dict[str, Dict[str, Any]]:
        """Get information for all active stops.
        
        Returns:
            Dictionary of symbol -> stop info
        """
        return {
            symbol: self.get_stop_info(symbol)
            for symbol in self.stop_orders.keys()
        }
