"""Paper trading broker implementation for simulation."""

import random
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
import pandas as pd
import numpy as np

from .base_broker import BaseBroker, Order, Position, OrderStatus, OrderType, OrderSide
from ..utils import get_logger, config


class PaperBroker(BaseBroker):
    """Paper trading broker for simulation."""
    
    def __init__(self, initial_capital: Optional[float] = None):
        super().__init__()
        self.logger = get_logger("trading_engine.broker.paper")
        self._connected = False
        
        # Initialize with paper trading capital
        self.initial_capital = initial_capital or config.initial_capital
        self.cash_balance = self.initial_capital
        self.commission_per_trade = config.commission_per_trade
        
        # Simulation parameters
        self.slippage = config.paper_slippage
        self.latency_ms = config.paper_latency_ms
        
        # Market data storage
        self.market_data: Dict[str, Dict[str, Any]] = {}
        self.historical_data: Optional[pd.DataFrame] = None
        self.current_time: Optional[datetime] = None
        
        # Order execution queue
        self.pending_orders: List[Order] = []
        
        # Performance tracking
        self.trade_history: List[Dict[str, Any]] = []
        self.daily_pnl_history: List[float] = []
        self.start_date: Optional[datetime] = None
        
        self.logger.info(f"Initialized paper broker with ${self.initial_capital:,.2f}")
    
    def connect(self) -> bool:
        """Connect to paper trading simulation."""
        self._connected = True
        self.start_date = datetime.now()
        self.logger.info("Connected to paper trading simulation")
        return True
    
    def disconnect(self):
        """Disconnect from paper trading simulation."""
        self._connected = False
        self.logger.info("Disconnected from paper trading simulation")
    
    def is_connected(self) -> bool:
        """Check if connected."""
        return self._connected
    
    def load_historical_data(self, data: pd.DataFrame):
        """Load historical data for backtesting.
        
        Args:
            data: DataFrame with columns ['timestamp', 'symbol', 'open', 'high', 'low', 'close', 'volume']
        """
        self.historical_data = data.copy()
        self.logger.info(f"Loaded historical data: {len(data)} rows")
    
    def update_market_data(self, timestamp: datetime, market_data: Dict[str, Dict[str, Any]]):
        """Update market data for simulation.
        
        Args:
            timestamp: Current simulation timestamp
            market_data: Dictionary of symbol -> price data
        """
        self.current_time = timestamp
        self.market_data.update(market_data)
        
        # Update position market prices
        for symbol, position in self.positions.items():
            if symbol in market_data:
                price = market_data[symbol].get('close', position.market_price)
                position.update_market_price(price)
        
        # Process pending orders
        self._process_pending_orders()
    
    def get_account_info(self) -> Dict[str, Any]:
        """Get account information."""
        portfolio_value = self.get_portfolio_value()
        total_pnl = self.get_total_pnl()
        
        return {
            'account_number': 'PAPER_ACCOUNT',
            'buying_power': self.cash_balance,
            'cash': self.cash_balance,
            'portfolio_value': portfolio_value,
            'total_pnl': total_pnl,
            'day_trade_count': 0,
            'pattern_day_trader': False,
            'initial_capital': self.initial_capital,
            'total_return_pct': ((portfolio_value / self.initial_capital) - 1) * 100 if self.initial_capital > 0 else 0
        }
    
    def get_quote(self, symbol: str) -> Dict[str, Any]:
        """Get current quote for a symbol."""
        if symbol in self.market_data:
            data = self.market_data[symbol]
            
            # Simulate bid-ask spread
            last_price = data.get('close', 0)
            spread = last_price * 0.001  # 0.1% spread
            
            return {
                'symbol': symbol,
                'last_trade_price': last_price,
                'bid_price': last_price - spread/2,
                'ask_price': last_price + spread/2,
                'bid_size': random.randint(100, 1000),
                'ask_size': random.randint(100, 1000),
                'volume': data.get('volume', 0),
                'updated_at': self.current_time.isoformat() if self.current_time else datetime.now().isoformat()
            }
        
        return {'symbol': symbol, 'last_trade_price': 0}
    
    def place_order(
        self,
        symbol: str,
        side: OrderSide,
        order_type: OrderType,
        quantity: int,
        price: Optional[float] = None,
        stop_price: Optional[float] = None,
        **kwargs
    ) -> Order:
        """Place an order."""
        if not self.is_connected():
            raise RuntimeError("Not connected to broker")
        
        # Create order
        order = Order(
            symbol=symbol,
            side=side,
            order_type=order_type,
            quantity=quantity,
            price=price,
            stop_price=stop_price,
            status=OrderStatus.PENDING
        )
        
        # Basic validation
        if quantity <= 0:
            order.status = OrderStatus.REJECTED
            self.logger.warning(f"Order rejected: Invalid quantity {quantity}")
            return order
        
        # Check buying power for buy orders
        if side == OrderSide.BUY:
            quote = self.get_quote(symbol)
            estimated_cost = quantity * (price or quote.get('last_trade_price', 0))
            
            if estimated_cost > self.cash_balance:
                order.status = OrderStatus.REJECTED
                self.logger.warning(f"Order rejected: Insufficient buying power. Need ${estimated_cost:.2f}, have ${self.cash_balance:.2f}")
                return order
        
        # Check position for sell orders
        if side == OrderSide.SELL:
            current_position = self.positions.get(symbol)
            if not current_position or current_position.quantity < quantity:
                order.status = OrderStatus.REJECTED
                available = current_position.quantity if current_position else 0
                self.logger.warning(f"Order rejected: Insufficient shares. Need {quantity}, have {available}")
                return order
        
        # Add to pending orders for processing
        order.status = OrderStatus.QUEUED
        self.pending_orders.append(order)
        self.orders[order.id] = order
        
        self.logger.info(f"Queued order: {side.value} {quantity} {symbol} @ {price or 'market'}")
        return order
    
    def cancel_order(self, order_id: str) -> bool:
        """Cancel an order."""
        if order_id in self.orders:
            order = self.orders[order_id]
            if order.is_active:
                order.status = OrderStatus.CANCELLED
                order.updated_at = datetime.now()
                
                # Remove from pending orders
                self.pending_orders = [o for o in self.pending_orders if o.id != order_id]
                
                self.logger.info(f"Cancelled order: {order_id}")
                return True
        
        return False
    
    def get_order_status(self, order_id: str) -> Optional[Order]:
        """Get order status."""
        return self.orders.get(order_id)
    
    def get_positions(self) -> Dict[str, Position]:
        """Get all positions."""
        return self.positions.copy()
    
    def get_position(self, symbol: str) -> Optional[Position]:
        """Get position for a specific symbol."""
        return self.positions.get(symbol)

    def _process_pending_orders(self):
        """Process pending orders based on current market data."""
        orders_to_remove = []

        for order in self.pending_orders:
            if self._should_execute_order(order):
                if self._execute_order(order):
                    orders_to_remove.append(order)

        # Remove executed orders from pending list
        for order in orders_to_remove:
            if order in self.pending_orders:
                self.pending_orders.remove(order)

    def _should_execute_order(self, order: Order) -> bool:
        """Check if an order should be executed."""
        if order.symbol not in self.market_data:
            return False

        market_data = self.market_data[order.symbol]
        current_price = market_data.get('close', 0)

        if order.order_type == OrderType.MARKET:
            return True
        elif order.order_type == OrderType.LIMIT:
            if order.side == OrderSide.BUY:
                return current_price <= order.price
            else:  # SELL
                return current_price >= order.price
        elif order.order_type == OrderType.STOP:
            if order.side == OrderSide.BUY:
                return current_price >= order.stop_price
            else:  # SELL
                return current_price <= order.stop_price

        return False

    def _execute_order(self, order: Order) -> bool:
        """Execute an order."""
        try:
            # Simulate execution delay
            if self.latency_ms > 0:
                time.sleep(self.latency_ms / 1000.0)

            # Get execution price
            execution_price = self._get_execution_price(order)
            if execution_price <= 0:
                order.status = OrderStatus.REJECTED
                return True

            # Calculate commission
            commission = self.commission_per_trade

            # Execute the trade
            if order.side == OrderSide.BUY:
                total_cost = (order.quantity * execution_price) + commission

                if total_cost > self.cash_balance:
                    order.status = OrderStatus.REJECTED
                    self.logger.warning(f"Order rejected: Insufficient funds for {order.symbol}")
                    return True

                # Update cash
                self.cash_balance -= total_cost

                # Update position
                if order.symbol in self.positions:
                    position = self.positions[order.symbol]
                    total_shares = position.quantity + order.quantity
                    total_cost_basis = (position.quantity * position.average_price) + (order.quantity * execution_price)
                    position.quantity = total_shares
                    position.average_price = total_cost_basis / total_shares
                    position.updated_at = datetime.now()
                else:
                    self.positions[order.symbol] = Position(
                        symbol=order.symbol,
                        quantity=order.quantity,
                        average_price=execution_price,
                        market_price=execution_price
                    )

            else:  # SELL
                if order.symbol not in self.positions or self.positions[order.symbol].quantity < order.quantity:
                    order.status = OrderStatus.REJECTED
                    self.logger.warning(f"Order rejected: Insufficient shares for {order.symbol}")
                    return True

                # Calculate proceeds
                proceeds = (order.quantity * execution_price) - commission

                # Update cash
                self.cash_balance += proceeds

                # Update position
                position = self.positions[order.symbol]
                realized_pnl = (execution_price - position.average_price) * order.quantity
                position.realized_pnl += realized_pnl
                position.quantity -= order.quantity
                position.updated_at = datetime.now()

                # Remove position if quantity is zero
                if position.quantity == 0:
                    del self.positions[order.symbol]

            # Update order status
            order.status = OrderStatus.FILLED
            order.filled_quantity = order.quantity
            order.filled_price = execution_price
            order.commission = commission
            order.updated_at = datetime.now()

            # Record trade
            self._record_trade(order, execution_price, commission)

            self.logger.info(f"Executed order: {order.side.value} {order.quantity} {order.symbol} @ ${execution_price:.2f}")
            return True

        except Exception as e:
            self.logger.error(f"Error executing order {order.id}: {e}")
            order.status = OrderStatus.FAILED
            return True

    def _get_execution_price(self, order: Order) -> float:
        """Get execution price with slippage simulation."""
        if order.symbol not in self.market_data:
            return 0

        market_data = self.market_data[order.symbol]
        base_price = market_data.get('close', 0)

        if order.order_type == OrderType.MARKET:
            # Apply slippage for market orders
            slippage_factor = random.uniform(-self.slippage, self.slippage)
            if order.side == OrderSide.BUY:
                # Buy orders get worse prices (higher)
                slippage_factor = abs(slippage_factor)
            else:
                # Sell orders get worse prices (lower)
                slippage_factor = -abs(slippage_factor)

            execution_price = base_price * (1 + slippage_factor)
        else:
            # Limit orders execute at limit price or better
            execution_price = order.price

        return round(execution_price, 2)

    def _record_trade(self, order: Order, execution_price: float, commission: float):
        """Record trade for performance analysis."""
        trade_record = {
            'timestamp': self.current_time or datetime.now(),
            'order_id': order.id,
            'symbol': order.symbol,
            'side': order.side.value,
            'quantity': order.quantity,
            'price': execution_price,
            'commission': commission,
            'portfolio_value': self.get_portfolio_value()
        }

        self.trade_history.append(trade_record)

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        portfolio_value = self.get_portfolio_value()
        total_return = ((portfolio_value / self.initial_capital) - 1) * 100 if self.initial_capital > 0 else 0

        stats = {
            'initial_capital': self.initial_capital,
            'current_value': portfolio_value,
            'total_return_pct': total_return,
            'total_pnl': portfolio_value - self.initial_capital,
            'cash_balance': self.cash_balance,
            'positions_value': sum(pos.market_value for pos in self.positions.values()),
            'total_trades': len(self.trade_history),
            'unrealized_pnl': sum(pos.unrealized_pnl for pos in self.positions.values()),
            'realized_pnl': sum(pos.realized_pnl for pos in self.positions.values())
        }

        if self.trade_history:
            # Calculate additional metrics
            returns = []
            for i in range(1, len(self.trade_history)):
                prev_value = self.trade_history[i-1]['portfolio_value']
                curr_value = self.trade_history[i]['portfolio_value']
                if prev_value > 0:
                    returns.append((curr_value / prev_value) - 1)

            if returns:
                stats['volatility'] = np.std(returns) * np.sqrt(252) * 100  # Annualized
                stats['sharpe_ratio'] = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0
                stats['max_drawdown'] = self._calculate_max_drawdown()

        return stats

    def _calculate_max_drawdown(self) -> float:
        """Calculate maximum drawdown."""
        if not self.trade_history:
            return 0.0

        values = [trade['portfolio_value'] for trade in self.trade_history]
        peak = values[0]
        max_dd = 0.0

        for value in values:
            if value > peak:
                peak = value
            drawdown = (peak - value) / peak
            max_dd = max(max_dd, drawdown)

        return max_dd * 100
