"""Pytest configuration and fixtures for trading engine tests."""

import pytest
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List
import numpy as np

from src.broker import PaperBroker, Position
from src.data_feed import HistoricalFeed, OHLCV, MarketData
from src.strategies import StrategyEngine, TTMSqueezeStrategy
from src.risk import RiskManager
from src.utils import config


@pytest.fixture
def sample_symbols():
    """Sample symbols for testing."""
    return ['AAPL', 'GOOGL', 'MSFT']


@pytest.fixture
def sample_ohlcv_data():
    """Sample OHLCV data for testing."""
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    data = []
    
    symbols = ['AAPL', 'GOOGL', 'MSFT']
    initial_prices = {'AAPL': 150.0, 'GOOGL': 100.0, 'MSFT': 250.0}
    
    for symbol in symbols:
        price = initial_prices[symbol]
        
        for date in dates:
            # Generate random walk
            daily_return = np.random.normal(0.001, 0.02)
            price *= (1 + daily_return)
            
            # Generate OHLC
            volatility = np.random.uniform(0.01, 0.03)
            high = price * (1 + volatility)
            low = price * (1 - volatility)
            open_price = np.random.uniform(low, high)
            volume = int(np.random.uniform(100000, 1000000))
            
            data.append({
                'timestamp': date,
                'symbol': symbol,
                'open': round(open_price, 2),
                'high': round(high, 2),
                'low': round(low, 2),
                'close': round(price, 2),
                'volume': volume
            })
    
    return pd.DataFrame(data)


@pytest.fixture
def paper_broker():
    """Paper broker instance for testing."""
    broker = PaperBroker(initial_capital=10000.0)
    broker.connect()
    return broker


@pytest.fixture
def historical_feed(sample_symbols, sample_ohlcv_data):
    """Historical data feed for testing."""
    feed = HistoricalFeed(
        symbols=sample_symbols,
        data_source=sample_ohlcv_data,
        start_date=datetime(2023, 1, 1),
        end_date=datetime(2023, 12, 31)
    )
    feed.connect()
    return feed


@pytest.fixture
def strategy_engine(sample_symbols):
    """Strategy engine with default strategies."""
    engine = StrategyEngine()
    engine.create_default_strategies(sample_symbols)
    return engine


@pytest.fixture
def risk_manager(paper_broker):
    """Risk manager instance."""
    return RiskManager(paper_broker)


@pytest.fixture
def sample_market_data():
    """Sample market data for testing."""
    timestamp = datetime.now()
    data = {
        'AAPL': OHLCV(
            timestamp=timestamp,
            symbol='AAPL',
            open=150.0,
            high=152.0,
            low=149.0,
            close=151.0,
            volume=1000000
        ),
        'GOOGL': OHLCV(
            timestamp=timestamp,
            symbol='GOOGL',
            open=100.0,
            high=101.0,
            low=99.0,
            close=100.5,
            volume=500000
        )
    }
    
    return MarketData(timestamp=timestamp, data=data)


@pytest.fixture
def sample_position():
    """Sample position for testing."""
    return Position(
        symbol='AAPL',
        quantity=100,
        average_price=150.0,
        market_price=151.0
    )


@pytest.fixture(autouse=True)
def reset_config():
    """Reset configuration for each test."""
    # Store original values
    original_mode = config.mode
    original_initial_capital = config.initial_capital
    
    # Set test values
    config.mode = 'paper'
    config.initial_capital = 10000.0
    
    yield
    
    # Restore original values
    config.mode = original_mode
    config.initial_capital = original_initial_capital


@pytest.fixture
def mock_market_data_stream():
    """Mock market data stream for testing."""
    def generate_stream(symbols: List[str], num_periods: int = 10):
        base_prices = {'AAPL': 150.0, 'GOOGL': 100.0, 'MSFT': 250.0}
        
        for i in range(num_periods):
            timestamp = datetime.now() + timedelta(minutes=i)
            data = {}
            
            for symbol in symbols:
                price = base_prices[symbol] * (1 + np.random.normal(0, 0.01))
                base_prices[symbol] = price
                
                data[symbol] = OHLCV(
                    timestamp=timestamp,
                    symbol=symbol,
                    open=price * 0.999,
                    high=price * 1.002,
                    low=price * 0.998,
                    close=price,
                    volume=int(np.random.uniform(100000, 1000000))
                )
            
            yield MarketData(timestamp=timestamp, data=data)
    
    return generate_stream


@pytest.fixture
def integration_test_data():
    """Data for integration tests."""
    return {
        'symbols': ['AAPL', 'GOOGL'],
        'initial_capital': 10000.0,
        'test_duration_days': 30,
        'expected_min_signals': 5,
        'expected_max_drawdown': 0.10  # 10%
    }
