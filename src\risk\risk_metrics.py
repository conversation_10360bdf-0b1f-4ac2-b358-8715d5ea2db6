"""Risk metrics calculations and utilities."""

import math
from typing import List, Dict, Any, Optional
import numpy as np
from scipy import stats


class RiskMetrics:
    """Collection of risk metric calculations."""
    
    @staticmethod
    def value_at_risk(returns: List[float], confidence_level: float = 0.05) -> float:
        """Calculate Value at Risk (VaR).
        
        Args:
            returns: List of returns
            confidence_level: Confidence level (e.g., 0.05 for 95% VaR)
            
        Returns:
            VaR value
        """
        if not returns or len(returns) < 2:
            return 0.0
        
        return np.percentile(returns, confidence_level * 100)
    
    @staticmethod
    def conditional_var(returns: List[float], confidence_level: float = 0.05) -> float:
        """Calculate Conditional Value at Risk (CVaR/Expected Shortfall).
        
        Args:
            returns: List of returns
            confidence_level: Confidence level
            
        Returns:
            CVaR value
        """
        if not returns or len(returns) < 2:
            return 0.0
        
        var = RiskMetrics.value_at_risk(returns, confidence_level)
        tail_returns = [r for r in returns if r <= var]
        
        return np.mean(tail_returns) if tail_returns else 0.0
    
    @staticmethod
    def maximum_drawdown(values: List[float]) -> Dict[str, float]:
        """Calculate maximum drawdown and related metrics.
        
        Args:
            values: List of portfolio values
            
        Returns:
            Dictionary with drawdown metrics
        """
        if not values or len(values) < 2:
            return {'max_drawdown': 0.0, 'max_drawdown_pct': 0.0, 'recovery_time': 0}
        
        peak = values[0]
        max_dd = 0.0
        max_dd_pct = 0.0
        peak_idx = 0
        trough_idx = 0
        recovery_time = 0
        
        for i, value in enumerate(values):
            if value > peak:
                peak = value
                peak_idx = i
            
            drawdown = peak - value
            drawdown_pct = drawdown / peak if peak > 0 else 0
            
            if drawdown_pct > max_dd_pct:
                max_dd = drawdown
                max_dd_pct = drawdown_pct
                trough_idx = i
                
                # Calculate recovery time
                recovery_time = 0
                for j in range(i + 1, len(values)):
                    if values[j] >= peak:
                        recovery_time = j - i
                        break
                else:
                    recovery_time = len(values) - i - 1  # Still in drawdown
        
        return {
            'max_drawdown': max_dd,
            'max_drawdown_pct': max_dd_pct * 100,
            'recovery_time': recovery_time,
            'peak_idx': peak_idx,
            'trough_idx': trough_idx
        }
    
    @staticmethod
    def sharpe_ratio(returns: List[float], risk_free_rate: float = 0.02) -> float:
        """Calculate Sharpe ratio.
        
        Args:
            returns: List of returns
            risk_free_rate: Annual risk-free rate
            
        Returns:
            Sharpe ratio
        """
        if not returns or len(returns) < 2:
            return 0.0
        
        excess_returns = np.array(returns) - (risk_free_rate / 252)  # Daily risk-free rate
        
        if np.std(excess_returns) == 0:
            return 0.0
        
        return np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252)
    
    @staticmethod
    def sortino_ratio(returns: List[float], risk_free_rate: float = 0.02) -> float:
        """Calculate Sortino ratio (downside deviation).
        
        Args:
            returns: List of returns
            risk_free_rate: Annual risk-free rate
            
        Returns:
            Sortino ratio
        """
        if not returns or len(returns) < 2:
            return 0.0
        
        excess_returns = np.array(returns) - (risk_free_rate / 252)
        downside_returns = excess_returns[excess_returns < 0]
        
        if len(downside_returns) == 0:
            return float('inf')
        
        downside_deviation = np.sqrt(np.mean(downside_returns ** 2))
        
        if downside_deviation == 0:
            return 0.0
        
        return np.mean(excess_returns) / downside_deviation * np.sqrt(252)
    
    @staticmethod
    def calmar_ratio(returns: List[float], values: List[float]) -> float:
        """Calculate Calmar ratio (annual return / max drawdown).
        
        Args:
            returns: List of returns
            values: List of portfolio values
            
        Returns:
            Calmar ratio
        """
        if not returns or not values or len(returns) < 2:
            return 0.0
        
        annual_return = np.mean(returns) * 252
        max_dd = RiskMetrics.maximum_drawdown(values)['max_drawdown_pct']
        
        if max_dd == 0:
            return float('inf')
        
        return annual_return / (max_dd / 100)
    
    @staticmethod
    def beta(returns: List[float], benchmark_returns: List[float]) -> float:
        """Calculate beta relative to benchmark.
        
        Args:
            returns: Portfolio returns
            benchmark_returns: Benchmark returns
            
        Returns:
            Beta value
        """
        if (not returns or not benchmark_returns or 
            len(returns) != len(benchmark_returns) or len(returns) < 2):
            return 0.0
        
        covariance = np.cov(returns, benchmark_returns)[0][1]
        benchmark_variance = np.var(benchmark_returns)
        
        if benchmark_variance == 0:
            return 0.0
        
        return covariance / benchmark_variance
    
    @staticmethod
    def alpha(
        returns: List[float], 
        benchmark_returns: List[float], 
        risk_free_rate: float = 0.02
    ) -> float:
        """Calculate alpha (excess return over CAPM prediction).
        
        Args:
            returns: Portfolio returns
            benchmark_returns: Benchmark returns
            risk_free_rate: Annual risk-free rate
            
        Returns:
            Alpha value
        """
        if (not returns or not benchmark_returns or 
            len(returns) != len(benchmark_returns) or len(returns) < 2):
            return 0.0
        
        portfolio_return = np.mean(returns) * 252
        benchmark_return = np.mean(benchmark_returns) * 252
        beta_value = RiskMetrics.beta(returns, benchmark_returns)
        
        expected_return = risk_free_rate + beta_value * (benchmark_return - risk_free_rate)
        
        return portfolio_return - expected_return
    
    @staticmethod
    def information_ratio(returns: List[float], benchmark_returns: List[float]) -> float:
        """Calculate information ratio.
        
        Args:
            returns: Portfolio returns
            benchmark_returns: Benchmark returns
            
        Returns:
            Information ratio
        """
        if (not returns or not benchmark_returns or 
            len(returns) != len(benchmark_returns) or len(returns) < 2):
            return 0.0
        
        excess_returns = np.array(returns) - np.array(benchmark_returns)
        tracking_error = np.std(excess_returns)
        
        if tracking_error == 0:
            return 0.0
        
        return np.mean(excess_returns) / tracking_error * np.sqrt(252)
    
    @staticmethod
    def win_rate(returns: List[float]) -> float:
        """Calculate win rate (percentage of positive returns).
        
        Args:
            returns: List of returns
            
        Returns:
            Win rate as percentage
        """
        if not returns:
            return 0.0
        
        winning_periods = sum(1 for r in returns if r > 0)
        return (winning_periods / len(returns)) * 100
    
    @staticmethod
    def profit_factor(returns: List[float]) -> float:
        """Calculate profit factor (gross profit / gross loss).
        
        Args:
            returns: List of returns
            
        Returns:
            Profit factor
        """
        if not returns:
            return 0.0
        
        gross_profit = sum(r for r in returns if r > 0)
        gross_loss = abs(sum(r for r in returns if r < 0))
        
        if gross_loss == 0:
            return float('inf') if gross_profit > 0 else 0.0
        
        return gross_profit / gross_loss
    
    @staticmethod
    def calculate_portfolio_risk_metrics(
        returns: List[float],
        values: List[float],
        benchmark_returns: Optional[List[float]] = None,
        risk_free_rate: float = 0.02
    ) -> Dict[str, Any]:
        """Calculate comprehensive risk metrics for a portfolio.
        
        Args:
            returns: Portfolio returns
            values: Portfolio values
            benchmark_returns: Benchmark returns (optional)
            risk_free_rate: Annual risk-free rate
            
        Returns:
            Dictionary with all risk metrics
        """
        metrics = {}
        
        if returns and len(returns) > 1:
            # Basic metrics
            metrics['total_return'] = (values[-1] / values[0] - 1) * 100 if values and len(values) > 1 else 0
            metrics['annualized_return'] = np.mean(returns) * 252 * 100
            metrics['volatility'] = np.std(returns) * np.sqrt(252) * 100
            metrics['sharpe_ratio'] = RiskMetrics.sharpe_ratio(returns, risk_free_rate)
            metrics['sortino_ratio'] = RiskMetrics.sortino_ratio(returns, risk_free_rate)
            
            # Drawdown metrics
            dd_metrics = RiskMetrics.maximum_drawdown(values)
            metrics.update(dd_metrics)
            
            # Risk metrics
            metrics['var_95'] = RiskMetrics.value_at_risk(returns, 0.05) * 100
            metrics['cvar_95'] = RiskMetrics.conditional_var(returns, 0.05) * 100
            metrics['calmar_ratio'] = RiskMetrics.calmar_ratio(returns, values)
            
            # Performance metrics
            metrics['win_rate'] = RiskMetrics.win_rate(returns)
            metrics['profit_factor'] = RiskMetrics.profit_factor(returns)
            
            # Benchmark comparison (if provided)
            if benchmark_returns and len(benchmark_returns) == len(returns):
                metrics['beta'] = RiskMetrics.beta(returns, benchmark_returns)
                metrics['alpha'] = RiskMetrics.alpha(returns, benchmark_returns, risk_free_rate) * 100
                metrics['information_ratio'] = RiskMetrics.information_ratio(returns, benchmark_returns)
        
        return metrics
