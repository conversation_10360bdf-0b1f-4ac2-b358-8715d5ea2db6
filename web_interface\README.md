# Robinhood Trading Engine Web Interface

A modern, responsive web interface for monitoring and controlling the Robinhood trading engine. Built with Flask, Socket.IO, and Bootstrap for real-time trading management.

## 🌟 Features

### 📊 **Real-time Dashboard**
- Live portfolio value and performance metrics
- Real-time position monitoring with P&L tracking
- Interactive charts for portfolio performance and allocation
- Strategy status and recent signals display
- Risk management alerts and controls

### 📈 **Advanced Analytics**
- Comprehensive performance analysis with key metrics
- Interactive equity curve and drawdown charts
- Strategy performance comparison and analysis
- Monthly returns and volatility analysis
- Risk metrics including Sharpe ratio, VaR, and Calmar ratio
- Trade history with export functionality

### ⚙️ **Configuration Management**
- Engine configuration (paper/live mode, symbols, intervals)
- Risk management parameters (position limits, drawdowns)
- Strategy parameter tuning for all algorithms
- Settings export/import for backup and sharing

### 🔄 **Real-time Updates**
- WebSocket connections for live data streaming
- Automatic refresh of all metrics and charts
- Connection status monitoring
- Real-time alerts and notifications

### 🛡️ **Safety Controls**
- Emergency stop functionality
- Risk limit monitoring and alerts
- Strategy activation/deactivation controls
- Live trading safety warnings

## 🚀 Quick Start

### Prerequisites
```bash
# Install Python dependencies
pip install -r requirements.txt

# Ensure the main trading engine is set up
cd ..
pip install -r requirements.txt
```

### Running the Interface

#### Development Mode
```bash
# Start the web interface
python run.py

# Or using Flask directly
python app.py
```

#### Production Mode
```bash
# Using Gunicorn (recommended for production)
gunicorn --worker-class eventlet -w 1 --bind 0.0.0.0:5000 app:app

# Or with custom configuration
gunicorn --worker-class eventlet -w 1 --bind 0.0.0.0:5000 --timeout 120 app:app
```

### Environment Variables
```bash
# Optional configuration
export FLASK_DEBUG=false          # Enable/disable debug mode
export FLASK_HOST=0.0.0.0         # Host to bind to
export FLASK_PORT=5000            # Port to run on
```

## 📱 Interface Overview

### Dashboard (`/`)
The main control center for your trading engine:

- **Engine Controls**: Initialize, start, stop, and emergency stop
- **Portfolio Metrics**: Real-time value, cash, P&L, and returns
- **Live Charts**: Portfolio performance and position allocation
- **Position Table**: Current holdings with real-time P&L
- **Strategy Status**: Active strategies with toggle controls
- **Recent Signals**: Latest trading signals with confidence scores
- **Risk Management**: Current risk status and alerts

### Analytics (`/analytics`)
Comprehensive performance analysis:

- **Performance Metrics**: Total return, Sharpe ratio, max drawdown, win rate
- **Equity Curve**: Interactive portfolio value over time
- **Strategy Analysis**: Individual strategy performance comparison
- **Monthly Returns**: Bar chart of monthly performance
- **Drawdown Analysis**: Underwater curve showing drawdowns
- **Risk Metrics**: Detailed risk analysis and volatility charts
- **Trade History**: Complete trade log with export functionality

### Settings (`/settings`)
Configuration and parameter management:

- **Engine Configuration**: Trading mode, capital, symbols, intervals
- **Risk Management**: Position limits, drawdown limits, exposure controls
- **Strategy Parameters**: Fine-tune all strategy algorithms
- **Export/Import**: Backup and restore configurations

## 🔧 API Endpoints

### Engine Control
- `GET /api/status` - Get engine status
- `POST /api/initialize` - Initialize the trading engine
- `POST /api/start` - Start trading
- `POST /api/stop` - Stop trading
- `POST /api/emergency-stop` - Emergency stop
- `POST /api/clear-emergency` - Clear emergency stop

### Data Access
- `GET /api/portfolio` - Get portfolio data
- `GET /api/strategies` - Get strategy information
- `GET /api/risk` - Get risk management status

### Strategy Control
- `POST /api/strategy/<name>/toggle` - Toggle strategy active/inactive

### WebSocket Events
- `connect` - Client connection established
- `disconnect` - Client disconnection
- `real_time_update` - Live data updates (every 2 seconds)

## 🎨 UI Components

### Charts and Visualizations
- **Portfolio Performance**: Line chart with real-time updates
- **Position Allocation**: Doughnut chart showing portfolio distribution
- **Equity Curve**: Historical performance with timeframe selection
- **Strategy Performance**: Pie chart comparing strategy contributions
- **Monthly Returns**: Bar chart with positive/negative coloring
- **Drawdown Analysis**: Area chart showing portfolio drawdowns
- **Volatility Chart**: Rolling volatility analysis

### Interactive Controls
- **Strategy Toggles**: Enable/disable strategies with switches
- **Emergency Controls**: Prominent emergency stop button
- **Timeframe Selection**: Chart timeframe controls (1D, 1W, 1M, All)
- **Real-time Indicators**: Connection status and engine state
- **Alert Modals**: User feedback and confirmation dialogs

## 🔒 Security Considerations

### Development vs Production
- **Development**: Debug mode enabled, detailed error messages
- **Production**: Debug disabled, error logging, secure headers

### Access Control
- Currently designed for single-user local access
- For multi-user deployment, add authentication middleware
- Consider VPN or firewall restrictions for remote access

### Data Protection
- No sensitive credentials stored in frontend
- All trading operations go through backend API
- WebSocket connections use same-origin policy

## 🛠️ Customization

### Styling
- Bootstrap 5 with custom CSS variables
- Dark theme optimized for trading
- Responsive design for mobile/tablet access
- Color coding: green (positive), red (negative), blue (neutral)

### Adding New Features
1. **New API Endpoint**: Add route in `app.py`
2. **Frontend Component**: Create HTML template
3. **JavaScript Integration**: Add to appropriate template
4. **Real-time Updates**: Extend WebSocket handler

### Chart Customization
- Charts use Chart.js library
- Easy to modify colors, types, and data
- Responsive and mobile-friendly
- Dark theme compatible

## 📊 Performance Monitoring

### Real-time Metrics
- Portfolio value updates every 2 seconds
- Position P&L calculated in real-time
- Strategy signals displayed immediately
- Risk alerts triggered automatically

### Historical Analysis
- Performance data stored and visualized
- Strategy comparison and analysis
- Risk metrics calculated continuously
- Export functionality for external analysis

## 🐛 Troubleshooting

### Common Issues

**Connection Problems**
```bash
# Check if the trading engine is running
python -c "from src.orchestrator import TradingOrchestrator; print('Engine available')"

# Verify WebSocket connection
# Look for "Connected to server" in browser console
```

**Chart Not Loading**
```bash
# Check browser console for JavaScript errors
# Ensure Chart.js CDN is accessible
# Verify data format in API responses
```

**Real-time Updates Not Working**
```bash
# Check WebSocket connection status
# Verify Socket.IO version compatibility
# Check firewall/proxy settings
```

### Debug Mode
```bash
# Enable debug mode for detailed error messages
export FLASK_DEBUG=true
python run.py
```

## 🔄 Updates and Maintenance

### Regular Updates
- Monitor for new trading engine features
- Update chart data sources as needed
- Review and update risk parameters
- Test emergency procedures regularly

### Backup Configuration
- Export settings regularly using the interface
- Store configuration files securely
- Document any custom modifications
- Test restore procedures

## 📞 Support

For issues related to:
- **Trading Engine**: Check main project documentation
- **Web Interface**: Review browser console and server logs
- **Configuration**: Use the settings page export/import
- **Performance**: Monitor real-time metrics and analytics

The web interface is designed to be intuitive and self-documenting. Most features include tooltips and help text to guide users through the trading engine management process.
