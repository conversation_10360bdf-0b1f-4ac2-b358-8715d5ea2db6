"""EMA Crossover strategy implementation."""

from datetime import datetime
from typing import Dict, List, Optional, Any
import pandas as pd
import numpy as np

from .base_strategy import BaseStrategy, Signal, SignalType, StrategyResult
from .technical_indicators import TechnicalIndicators
from ..data_feed import MarketData, OHLCV
from ..broker import OrderType
from ..utils import get_logger, config


class EMACrossoverStrategy(BaseStrategy):
    """EMA Crossover strategy for trend following."""
    
    def __init__(self, symbols: List[str], parameters: Optional[Dict[str, Any]] = None):
        """Initialize EMA Crossover strategy."""
        default_params = {
            'fast_period': config.ema_short_period,  # 12
            'slow_period': config.ema_long_period,   # 26
            'signal_period': 9,                      # MACD signal line
            'min_trend_strength': 0.02,              # Minimum trend strength (2%)
            'atr_multiplier': 2.0,                   # ATR multiplier for stop loss
            'profit_target_ratio': 2.0,              # Profit target ratio
            'max_history': 200
        }
        
        if parameters:
            default_params.update(parameters)
        
        super().__init__("EMA_Crossover", symbols, default_params)
        self.logger = get_logger("trading_engine.strategy.ema_crossover")
        
        # Track crossover state
        self.last_crossover: Dict[str, str] = {}  # 'bullish', 'bearish', or 'none'
    
    def generate_signals(self, market_data: MarketData) -> StrategyResult:
        """Generate EMA crossover signals."""
        if not self.is_active:
            return StrategyResult(self.name, market_data.timestamp, [])
        
        signals = []
        indicators = {}
        
        for symbol in self.symbols:
            if symbol not in market_data.data:
                continue
            
            try:
                signal = self._analyze_symbol(symbol, market_data.timestamp)
                if signal:
                    signals.append(signal)
                    self.record_signal(signal)
                
                symbol_indicators = self._get_indicators(symbol)
                if symbol_indicators:
                    indicators[symbol] = symbol_indicators
                    
            except Exception as e:
                self.logger.error(f"Error analyzing {symbol}: {e}")
                continue
        
        self.last_update = market_data.timestamp
        
        return StrategyResult(
            strategy_name=self.name,
            timestamp=market_data.timestamp,
            signals=signals,
            indicators=indicators
        )
    
    def update_data(self, market_data: MarketData):
        """Update internal data with new market data."""
        for symbol, ohlcv in market_data.data.items():
            if symbol in self.symbols:
                self._update_symbol_data(symbol, ohlcv)
    
    def _analyze_symbol(self, symbol: str, timestamp: datetime) -> Optional[Signal]:
        """Analyze symbol for EMA crossover signals."""
        data = self.get_data_for_symbol(symbol)
        
        min_periods = max(self.get_parameter('fast_period'), self.get_parameter('slow_period')) + 10
        if len(data) < min_periods:
            return None
        
        close = data['close']
        
        # Calculate EMAs
        fast_ema = TechnicalIndicators.ema(close, self.get_parameter('fast_period'))
        slow_ema = TechnicalIndicators.ema(close, self.get_parameter('slow_period'))
        
        if len(fast_ema) < 2 or len(slow_ema) < 2:
            return None
        
        # Current and previous values
        fast_current = fast_ema.iloc[-1]
        fast_previous = fast_ema.iloc[-2]
        slow_current = slow_ema.iloc[-1]
        slow_previous = slow_ema.iloc[-2]
        current_price = close.iloc[-1]
        
        # Detect crossovers
        bullish_crossover = (fast_previous <= slow_previous) and (fast_current > slow_current)
        bearish_crossover = (fast_previous >= slow_previous) and (fast_current < slow_current)
        
        if not (bullish_crossover or bearish_crossover):
            return None
        
        # Check trend strength
        trend_strength = abs(fast_current - slow_current) / slow_current
        min_strength = self.get_parameter('min_trend_strength')
        
        if trend_strength < min_strength:
            return None
        
        # Generate signal
        if bullish_crossover:
            signal_type = SignalType.BUY
            confidence = min(0.9, 0.6 + trend_strength * 5)
        else:
            signal_type = SignalType.SELL
            confidence = min(0.9, 0.6 + trend_strength * 5)
        
        # Calculate stop loss and take profit
        atr_value = TechnicalIndicators.atr(
            data['high'], data['low'], data['close'],
            period=14
        ).iloc[-1]
        
        atr_multiplier = self.get_parameter('atr_multiplier')
        profit_ratio = self.get_parameter('profit_target_ratio')
        
        if signal_type == SignalType.BUY:
            stop_loss = current_price - (atr_value * atr_multiplier)
            take_profit = current_price + (atr_value * atr_multiplier * profit_ratio)
        else:
            stop_loss = current_price + (atr_value * atr_multiplier)
            take_profit = current_price - (atr_value * atr_multiplier * profit_ratio)
        
        # Update crossover state
        self.last_crossover[symbol] = 'bullish' if bullish_crossover else 'bearish'
        
        return Signal(
            symbol=symbol,
            signal_type=signal_type,
            timestamp=timestamp,
            price=current_price,
            confidence=confidence,
            order_type=OrderType.MARKET,
            stop_loss=stop_loss,
            take_profit=take_profit,
            metadata={
                'strategy': 'ema_crossover',
                'fast_ema': fast_current,
                'slow_ema': slow_current,
                'trend_strength': trend_strength,
                'crossover_type': self.last_crossover[symbol]
            }
        )
    
    def _get_indicators(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get current indicators for a symbol."""
        data = self.get_data_for_symbol(symbol)
        
        min_periods = max(self.get_parameter('fast_period'), self.get_parameter('slow_period'))
        if len(data) < min_periods:
            return None
        
        try:
            close = data['close']
            
            fast_ema = TechnicalIndicators.ema(close, self.get_parameter('fast_period'))
            slow_ema = TechnicalIndicators.ema(close, self.get_parameter('slow_period'))
            
            # MACD for additional confirmation
            macd_line, signal_line, histogram = TechnicalIndicators.macd(
                close,
                fast=self.get_parameter('fast_period'),
                slow=self.get_parameter('slow_period'),
                signal=self.get_parameter('signal_period')
            )
            
            return {
                'fast_ema': fast_ema.iloc[-1] if len(fast_ema) > 0 else None,
                'slow_ema': slow_ema.iloc[-1] if len(slow_ema) > 0 else None,
                'macd': macd_line.iloc[-1] if len(macd_line) > 0 else None,
                'macd_signal': signal_line.iloc[-1] if len(signal_line) > 0 else None,
                'macd_histogram': histogram.iloc[-1] if len(histogram) > 0 else None,
                'trend_direction': 'bullish' if fast_ema.iloc[-1] > slow_ema.iloc[-1] else 'bearish',
                'last_crossover': self.last_crossover.get(symbol, 'none'),
                'current_price': close.iloc[-1] if len(close) > 0 else None
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating indicators for {symbol}: {e}")
            return None
