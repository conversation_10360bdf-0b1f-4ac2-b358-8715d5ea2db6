"""Unit tests for risk management components."""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock

from src.risk import (
    PositionSizer, SizingMethod, StopLossManager, StopType,
    PortfolioMonitor, RiskManager, RiskMetrics
)
from src.broker import Position, Order, OrderSide, OrderType
from src.strategies import Signal, SignalType


class TestPositionSizer:
    """Test cases for PositionSizer."""
    
    def test_initialization(self):
        """Test position sizer initialization."""
        sizer = PositionSizer()
        
        assert sizer.default_risk_per_trade == 0.02  # 2%
        assert sizer.min_position_size == 1
        assert sizer.max_position_size == 10000
    
    def test_fixed_percent_sizing(self):
        """Test fixed percentage position sizing."""
        sizer = PositionSizer()
        
        shares = sizer.calculate_position_size(
            account_value=10000.0,
            entry_price=100.0,
            stop_loss_price=95.0,  # 5% stop loss
            method=SizingMethod.FIXED_PERCENT,
            risk_percent=0.02  # 2% risk
        )
        
        # Risk amount = 10000 * 0.02 = 200
        # Risk per share = 100 - 95 = 5
        # Shares = 200 / 5 = 40
        assert shares == 40
    
    def test_kelly_criterion_sizing(self):
        """Test Kelly criterion position sizing."""
        sizer = PositionSizer()
        
        shares = sizer.calculate_position_size(
            account_value=10000.0,
            entry_price=100.0,
            stop_loss_price=95.0,
            method=SizingMethod.KELLY_CRITERION,
            confidence=0.8,
            win_rate=0.6,
            avg_win=1.5,
            avg_loss=1.0
        )
        
        # Should return positive number of shares
        assert shares > 0
        assert isinstance(shares, int)
    
    def test_atr_based_sizing(self):
        """Test ATR-based position sizing."""
        sizer = PositionSizer()
        
        shares = sizer.calculate_position_size(
            account_value=10000.0,
            entry_price=100.0,
            stop_loss_price=95.0,
            method=SizingMethod.ATR_BASED,
            atr_value=2.0,
            atr_multiplier=2.0
        )
        
        assert shares > 0
        assert isinstance(shares, int)
    
    def test_volatility_adjusted_sizing(self):
        """Test volatility-adjusted position sizing."""
        sizer = PositionSizer()
        
        # High volatility should result in smaller position
        shares_high_vol = sizer.calculate_position_size(
            account_value=10000.0,
            entry_price=100.0,
            stop_loss_price=95.0,
            method=SizingMethod.VOLATILITY_ADJUSTED,
            volatility=0.40  # 40% volatility
        )
        
        # Low volatility should result in larger position
        shares_low_vol = sizer.calculate_position_size(
            account_value=10000.0,
            entry_price=100.0,
            stop_loss_price=95.0,
            method=SizingMethod.VOLATILITY_ADJUSTED,
            volatility=0.10  # 10% volatility
        )
        
        assert shares_high_vol < shares_low_vol
    
    def test_position_constraints(self):
        """Test position size constraints."""
        sizer = PositionSizer()
        sizer.max_position_size = 50  # Set low limit for testing
        
        shares = sizer.calculate_position_size(
            account_value=100000.0,  # Large account
            entry_price=1.0,         # Cheap stock
            stop_loss_price=0.95,
            method=SizingMethod.FIXED_PERCENT
        )
        
        # Should be capped at max_position_size
        assert shares <= sizer.max_position_size
    
    def test_sizing_recommendations(self):
        """Test getting sizing recommendations."""
        sizer = PositionSizer()
        
        recommendations = sizer.get_sizing_recommendation(
            account_value=10000.0,
            entry_price=100.0,
            stop_loss_price=95.0,
            confidence=0.7,
            volatility=0.20,
            atr_value=2.0
        )
        
        assert isinstance(recommendations, dict)
        assert 'fixed_percent' in recommendations
        assert 'kelly_criterion' in recommendations
        assert 'volatility_adjusted' in recommendations
        assert 'atr_based' in recommendations
        
        # Each recommendation should have required fields
        for method, rec in recommendations.items():
            assert 'shares' in rec
            assert 'position_value' in rec
            assert 'risk_amount' in rec
            assert 'risk_percent' in rec


class TestStopLossManager:
    """Test cases for StopLossManager."""
    
    def test_initialization(self):
        """Test stop loss manager initialization."""
        manager = StopLossManager()
        
        assert len(manager.stop_orders) == 0
        assert manager.default_atr_multiplier == 2.0
        assert manager.trailing_stop_percent == 0.05
    
    def test_add_fixed_stop_loss(self, sample_position):
        """Test adding fixed stop loss."""
        manager = StopLossManager()
        
        success = manager.add_stop_loss(
            symbol='AAPL',
            position=sample_position,
            stop_type=StopType.FIXED,
            stop_price=145.0
        )
        
        assert success
        assert 'AAPL' in manager.stop_orders
        
        stop_order = manager.stop_orders['AAPL']
        assert stop_order.stop_type == StopType.FIXED
        assert stop_order.stop_price == 145.0
        assert stop_order.is_active
    
    def test_add_atr_stop_loss(self, sample_position):
        """Test adding ATR-based stop loss."""
        manager = StopLossManager()
        
        success = manager.add_stop_loss(
            symbol='AAPL',
            position=sample_position,
            stop_type=StopType.ATR_BASED,
            atr_value=2.0
        )
        
        assert success
        assert 'AAPL' in manager.stop_orders
        
        stop_order = manager.stop_orders['AAPL']
        assert stop_order.stop_type == StopType.ATR_BASED
        # For long position, stop should be below entry price
        assert stop_order.stop_price < sample_position.average_price
    
    def test_trailing_stop_update(self, sample_position):
        """Test trailing stop price updates."""
        manager = StopLossManager()
        
        # Add trailing stop
        manager.add_stop_loss(
            symbol='AAPL',
            position=sample_position,
            stop_type=StopType.TRAILING,
            trail_percent=0.05
        )
        
        stop_order = manager.stop_orders['AAPL']
        original_stop = stop_order.stop_price
        
        # Update with higher price (should move stop up)
        manager._update_trailing_stop(stop_order, 160.0)
        
        # Stop should have moved up
        assert stop_order.stop_price > original_stop
    
    def test_stop_trigger_detection(self, sample_position):
        """Test stop loss trigger detection."""
        manager = StopLossManager()
        
        # Add stop loss at $145
        manager.add_stop_loss(
            symbol='AAPL',
            position=sample_position,
            stop_type=StopType.FIXED,
            stop_price=145.0
        )
        
        stop_order = manager.stop_orders['AAPL']
        
        # Price above stop - should not trigger
        assert not manager._should_trigger_stop(stop_order, 150.0, sample_position)
        
        # Price at stop - should trigger
        assert manager._should_trigger_stop(stop_order, 145.0, sample_position)
        
        # Price below stop - should trigger
        assert manager._should_trigger_stop(stop_order, 140.0, sample_position)
    
    def test_remove_stop(self, sample_position):
        """Test removing stop loss."""
        manager = StopLossManager()
        
        manager.add_stop_loss(
            symbol='AAPL',
            position=sample_position,
            stop_type=StopType.FIXED,
            stop_price=145.0
        )
        
        assert 'AAPL' in manager.stop_orders
        
        success = manager.remove_stop('AAPL')
        assert success
        assert 'AAPL' not in manager.stop_orders
        
        # Removing non-existent stop should return False
        assert not manager.remove_stop('GOOGL')


class TestPortfolioMonitor:
    """Test cases for PortfolioMonitor."""
    
    def test_initialization(self):
        """Test portfolio monitor initialization."""
        monitor = PortfolioMonitor()
        
        assert len(monitor.snapshots) == 0
        assert len(monitor.daily_returns) == 0
        assert monitor.max_daily_drawdown == 0.03
        assert monitor.peak_value == 0.0
    
    def test_portfolio_update(self):
        """Test portfolio update."""
        monitor = PortfolioMonitor()
        positions = {'AAPL': Position('AAPL', 100, 150.0, 151.0)}
        
        snapshot = monitor.update_portfolio(
            total_value=25100.0,
            cash_balance=10000.0,
            positions=positions
        )
        
        assert snapshot.total_value == 25100.0
        assert snapshot.cash_balance == 10000.0
        assert snapshot.positions_value == 15100.0  # 100 * 151
        assert snapshot.positions_count == 1
        assert len(monitor.snapshots) == 1
    
    def test_risk_limit_alerts(self):
        """Test risk limit alert generation."""
        monitor = PortfolioMonitor()
        monitor.start_of_day_value = 10000.0
        
        positions = {}
        
        # Update with significant loss (5% drawdown)
        snapshot = monitor.update_portfolio(
            total_value=9500.0,
            cash_balance=9500.0,
            positions=positions
        )
        
        # Should generate daily drawdown alert
        recent_alerts = monitor.get_recent_alerts(hours=1)
        drawdown_alerts = [a for a in recent_alerts if a['type'] == 'daily_drawdown_exceeded']
        
        assert len(drawdown_alerts) > 0
    
    def test_largest_position_calculation(self):
        """Test largest position percentage calculation."""
        monitor = PortfolioMonitor()
        
        positions = {
            'AAPL': Position('AAPL', 100, 150.0, 151.0),  # $15,100
            'GOOGL': Position('GOOGL', 50, 100.0, 101.0)   # $5,050
        }
        
        largest_pct = monitor._calculate_largest_position_pct(positions, 20000.0)
        
        # Largest position is AAPL at $15,100 / $20,000 = 75.5%
        assert abs(largest_pct - 75.5) < 0.1
    
    def test_drawdown_calculation(self):
        """Test maximum drawdown calculation."""
        monitor = PortfolioMonitor()
        monitor.peak_value = 10000.0
        
        # Update with lower value
        monitor._update_drawdown(9000.0)
        
        # Drawdown should be 10%
        assert abs(monitor.max_drawdown - 0.10) < 0.001
    
    def test_risk_metrics(self):
        """Test risk metrics calculation."""
        monitor = PortfolioMonitor()
        
        # Add some daily returns
        monitor.daily_returns = [0.01, -0.005, 0.02, -0.01, 0.015]
        
        # Add a snapshot
        positions = {}
        monitor.update_portfolio(10500.0, 10500.0, positions)
        
        metrics = monitor.get_risk_metrics()
        
        assert 'total_value' in metrics
        assert 'volatility_annualized' in metrics
        assert 'sharpe_ratio' in metrics
        assert 'win_rate_pct' in metrics


class TestRiskManager:
    """Test cases for RiskManager."""
    
    def test_initialization(self, paper_broker):
        """Test risk manager initialization."""
        risk_manager = RiskManager(paper_broker)
        
        assert risk_manager.broker == paper_broker
        assert risk_manager.position_sizer is not None
        assert risk_manager.stop_loss_manager is not None
        assert risk_manager.portfolio_monitor is not None
        assert not risk_manager.is_emergency_stop
    
    def test_signal_evaluation_approval(self, paper_broker):
        """Test signal evaluation - approval case."""
        risk_manager = RiskManager(paper_broker)
        
        signal = Signal(
            symbol='AAPL',
            signal_type=SignalType.BUY,
            timestamp=datetime.now(),
            price=150.0,
            confidence=0.8,
            stop_loss=145.0
        )
        
        market_data = {'close': 150.0, 'volume': 1000000}
        
        should_execute, position_size, reason = risk_manager.evaluate_signal(
            signal, market_data, atr_value=2.0
        )
        
        assert should_execute
        assert position_size > 0
        assert "approved" in reason.lower()
    
    def test_signal_evaluation_emergency_stop(self, paper_broker):
        """Test signal evaluation during emergency stop."""
        risk_manager = RiskManager(paper_broker)
        risk_manager.trigger_emergency_stop("Test emergency")
        
        signal = Signal(
            symbol='AAPL',
            signal_type=SignalType.BUY,
            timestamp=datetime.now(),
            price=150.0,
            confidence=0.8
        )
        
        market_data = {'close': 150.0}
        
        should_execute, position_size, reason = risk_manager.evaluate_signal(
            signal, market_data
        )
        
        assert not should_execute
        assert position_size is None
        assert "emergency stop" in reason.lower()
    
    def test_emergency_stop_management(self, paper_broker):
        """Test emergency stop trigger and clear."""
        risk_manager = RiskManager(paper_broker)
        
        # Initially not in emergency stop
        assert not risk_manager.is_emergency_stop
        
        # Trigger emergency stop
        risk_manager.trigger_emergency_stop("Test emergency")
        assert risk_manager.is_emergency_stop
        
        # Clear emergency stop
        risk_manager.clear_emergency_stop("Test clear")
        assert not risk_manager.is_emergency_stop
    
    def test_position_risk_controls(self, paper_broker, sample_position):
        """Test adding position risk controls."""
        risk_manager = RiskManager(paper_broker)
        
        signal = Signal(
            symbol='AAPL',
            signal_type=SignalType.BUY,
            timestamp=datetime.now(),
            price=150.0,
            stop_loss=145.0
        )
        
        success = risk_manager.add_position_risk_controls(
            symbol='AAPL',
            position=sample_position,
            signal=signal,
            atr_value=2.0
        )
        
        assert success
        assert 'AAPL' in risk_manager.stop_loss_manager.stop_orders
    
    def test_risk_status(self, paper_broker):
        """Test risk status reporting."""
        risk_manager = RiskManager(paper_broker)
        
        status = risk_manager.get_risk_status()
        
        assert 'emergency_stop_active' in status
        assert 'risk_limits_breached' in status
        assert 'portfolio_metrics' in status
        assert 'recent_alerts_count' in status
        assert 'active_stops_count' in status


class TestRiskMetrics:
    """Test cases for RiskMetrics."""
    
    def test_value_at_risk(self):
        """Test Value at Risk calculation."""
        returns = [-0.05, -0.02, 0.01, 0.03, -0.01, 0.02, -0.03, 0.01]
        
        var_95 = RiskMetrics.value_at_risk(returns, confidence_level=0.05)
        
        # VaR should be negative (representing loss)
        assert var_95 < 0
    
    def test_maximum_drawdown(self):
        """Test maximum drawdown calculation."""
        values = [100, 105, 110, 95, 90, 100, 105]
        
        dd_metrics = RiskMetrics.maximum_drawdown(values)
        
        assert 'max_drawdown' in dd_metrics
        assert 'max_drawdown_pct' in dd_metrics
        assert 'recovery_time' in dd_metrics
        
        # Max drawdown should be from 110 to 90 = 20/110 = 18.18%
        assert abs(dd_metrics['max_drawdown_pct'] - 18.18) < 0.1
    
    def test_sharpe_ratio(self):
        """Test Sharpe ratio calculation."""
        returns = [0.01, 0.02, -0.01, 0.015, 0.005, -0.005, 0.02]
        
        sharpe = RiskMetrics.sharpe_ratio(returns, risk_free_rate=0.02)
        
        # Should return a numeric value
        assert isinstance(sharpe, (int, float))
    
    def test_win_rate(self):
        """Test win rate calculation."""
        returns = [0.01, -0.02, 0.03, -0.01, 0.02]  # 3 positive, 2 negative
        
        win_rate = RiskMetrics.win_rate(returns)
        
        # Should be 60% (3/5)
        assert abs(win_rate - 60.0) < 0.1
    
    def test_profit_factor(self):
        """Test profit factor calculation."""
        returns = [0.02, -0.01, 0.03, -0.015, 0.01]  # Profits: 0.06, Losses: 0.025
        
        profit_factor = RiskMetrics.profit_factor(returns)
        
        # Should be 0.06 / 0.025 = 2.4
        assert abs(profit_factor - 2.4) < 0.1
    
    def test_comprehensive_metrics(self):
        """Test comprehensive portfolio risk metrics."""
        returns = [0.01, -0.005, 0.02, -0.01, 0.015, -0.008, 0.012]
        values = [10000, 10100, 10050, 10250, 10150, 10302, 10220, 10343]
        
        metrics = RiskMetrics.calculate_portfolio_risk_metrics(returns, values)
        
        # Check that all expected metrics are present
        expected_metrics = [
            'total_return', 'annualized_return', 'volatility',
            'sharpe_ratio', 'max_drawdown_pct', 'var_95',
            'win_rate', 'profit_factor'
        ]
        
        for metric in expected_metrics:
            assert metric in metrics
