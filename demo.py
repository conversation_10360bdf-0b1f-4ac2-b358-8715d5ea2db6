#!/usr/bin/env python3
"""Demo script for the Robinhood Trading Engine."""

import sys
import time
from pathlib import Path
from datetime import datetime, timedelta

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.orchestrator import TradingOrchestrator
from src.data_feed.data_downloader import DataDownloader
from src.utils import config


def run_paper_trading_demo():
    """Run a paper trading demonstration."""
    print("🚀 Robinhood Trading Engine Demo")
    print("=" * 50)
    
    # Demo configuration
    symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA']
    print(f"📈 Trading symbols: {', '.join(symbols)}")
    print(f"💰 Initial capital: ${config.initial_capital:,.2f}")
    print(f"🎯 Mode: {config.mode}")
    
    # Create orchestrator
    orchestrator = TradingOrchestrator(symbols)
    
    try:
        print("\n🔧 Initializing trading engine...")
        
        # Generate sample data if needed
        downloader = DataDownloader()
        data_files = downloader.list_data_files()
        
        if not data_files:
            print("📊 No historical data found, generating synthetic data...")
            synthetic_data = downloader.generate_synthetic_data(
                symbols=symbols,
                start_date=datetime.now() - timedelta(days=365),
                end_date=datetime.now()
            )
            print(f"✅ Generated {len(synthetic_data)} data points")
        else:
            print(f"📁 Found {len(data_files)} data files")
        
        # Initialize the engine
        if not orchestrator.initialize():
            print("❌ Failed to initialize trading engine")
            return
        
        print("✅ Trading engine initialized successfully")
        
        # Display initial status
        status = orchestrator.get_status()
        print(f"\n📊 Initial Status:")
        print(f"   Portfolio Value: ${status.get('portfolio_value', 0):,.2f}")
        print(f"   Cash Balance: ${status.get('cash_balance', 0):,.2f}")
        print(f"   Active Strategies: {status.get('strategy_stats', {}).get('active_strategies', 0)}")
        
        # Start the engine
        print("\n🚀 Starting trading engine...")
        if not orchestrator.start():
            print("❌ Failed to start trading engine")
            return
        
        print("✅ Trading engine started")
        print("⏱️  Running demo for 30 seconds...")
        
        # Run for a short time
        start_time = time.time()
        last_status_time = start_time
        
        while time.time() - start_time < 30:  # Run for 30 seconds
            time.sleep(1)
            
            # Print status every 5 seconds
            if time.time() - last_status_time >= 5:
                current_status = orchestrator.get_status()
                
                print(f"\n📈 Status Update ({time.time() - start_time:.0f}s):")
                print(f"   Portfolio Value: ${current_status.get('portfolio_value', 0):,.2f}")
                print(f"   Signals Processed: {current_status.get('total_signals_processed', 0)}")
                print(f"   Orders Placed: {current_status.get('total_orders_placed', 0)}")
                print(f"   Risk Stops: {current_status.get('total_risk_stops', 0)}")
                
                # Show recent signals
                recent_signals = orchestrator.strategy_engine.get_recent_signals(count=3)
                if recent_signals:
                    print(f"   Recent Signals:")
                    for signal in recent_signals[-3:]:
                        print(f"     • {signal.signal_type.value.upper()} {signal.symbol} @ ${signal.price:.2f} (conf: {signal.confidence:.2f})")
                
                last_status_time = time.time()
        
        print("\n⏹️  Stopping trading engine...")
        orchestrator.stop()
        
        # Final results
        final_status = orchestrator.get_status()
        print(f"\n📊 Final Results:")
        print(f"   Final Portfolio Value: ${final_status.get('portfolio_value', 0):,.2f}")
        print(f"   Total Signals Processed: {final_status.get('total_signals_processed', 0)}")
        print(f"   Total Orders Placed: {final_status.get('total_orders_placed', 0)}")
        print(f"   Total Risk Stops: {final_status.get('total_risk_stops', 0)}")
        
        # Performance metrics
        if hasattr(orchestrator.broker, 'get_performance_stats'):
            perf_stats = orchestrator.broker.get_performance_stats()
            
            total_return = perf_stats.get('total_return_pct', 0)
            total_trades = perf_stats.get('total_trades', 0)
            
            print(f"   Total Return: {total_return:.2f}%")
            print(f"   Total Trades: {total_trades}")
            
            if total_return != 0:
                if total_return > 0:
                    print("   📈 Profitable session!")
                else:
                    print("   📉 Loss in this session")
        
        # Risk metrics
        risk_status = final_status.get('risk_status', {})
        if risk_status:
            print(f"\n🛡️  Risk Management:")
            print(f"   Emergency Stop: {'🔴 ACTIVE' if risk_status.get('emergency_stop_active') else '🟢 Clear'}")
            print(f"   Risk Limits: {'🔴 BREACHED' if risk_status.get('risk_limits_breached') else '🟢 OK'}")
            print(f"   Active Stops: {risk_status.get('active_stops_count', 0)}")
            print(f"   Recent Alerts: {risk_status.get('recent_alerts_count', 0)}")
        
        print("\n🎉 Demo completed successfully!")
        print("💡 This was a paper trading simulation - no real money was used")
        
    except KeyboardInterrupt:
        print("\n⚠️  Demo interrupted by user")
        orchestrator.stop()
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        orchestrator.stop()
        raise


def run_strategy_analysis():
    """Run strategy analysis without trading."""
    print("\n🧠 Strategy Analysis Mode")
    print("=" * 30)
    
    symbols = ['AAPL', 'GOOGL']
    orchestrator = TradingOrchestrator(symbols)
    
    try:
        if not orchestrator.initialize():
            print("❌ Failed to initialize for analysis")
            return
        
        # Get strategy information
        strategy_stats = orchestrator.strategy_engine.get_performance_stats()
        
        print(f"📊 Strategy Overview:")
        print(f"   Total Strategies: {strategy_stats.get('total_strategies', 0)}")
        print(f"   Active Strategies: {strategy_stats.get('active_strategies', 0)}")
        
        # List strategies
        for strategy_name in orchestrator.strategy_engine.list_strategies():
            strategy = orchestrator.strategy_engine.get_strategy(strategy_name)
            print(f"\n🎯 {strategy_name}:")
            print(f"   Status: {'🟢 Active' if strategy.is_active else '🔴 Inactive'}")
            print(f"   Symbols: {', '.join(strategy.symbols)}")
            print(f"   Parameters: {len(strategy.parameters)} configured")
            
            # Show key parameters
            if strategy.parameters:
                key_params = list(strategy.parameters.items())[:3]  # Show first 3
                for key, value in key_params:
                    print(f"     • {key}: {value}")
        
        orchestrator.stop()
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        orchestrator.stop()


def main():
    """Main demo function."""
    print("🤖 Robinhood Trading Engine")
    print("Developed with robin_stocks integration")
    print("=" * 50)
    
    if len(sys.argv) > 1 and sys.argv[1] == "analysis":
        run_strategy_analysis()
    else:
        run_paper_trading_demo()


if __name__ == "__main__":
    main()
