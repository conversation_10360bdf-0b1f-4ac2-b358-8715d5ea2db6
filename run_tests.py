#!/usr/bin/env python3
"""Test runner for the trading engine."""

import sys
import os
import subprocess
import argparse
from pathlib import Path


def run_command(cmd, description):
    """Run a command and return success status."""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {cmd}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print("✅ SUCCESS")
        if result.stdout:
            print("STDOUT:")
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print("❌ FAILED")
        print(f"Exit code: {e.returncode}")
        if e.stdout:
            print("STDOUT:")
            print(e.stdout)
        if e.stderr:
            print("STDERR:")
            print(e.stderr)
        return False


def main():
    """Main test runner."""
    parser = argparse.ArgumentParser(description="Run trading engine tests")
    parser.add_argument("--unit", action="store_true", help="Run unit tests only")
    parser.add_argument("--integration", action="store_true", help="Run integration tests only")
    parser.add_argument("--coverage", action="store_true", help="Run with coverage report")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--fast", action="store_true", help="Skip slow tests")
    parser.add_argument("--lint", action="store_true", help="Run linting checks")
    parser.add_argument("--type-check", action="store_true", help="Run type checking")
    
    args = parser.parse_args()
    
    # Set up environment
    project_root = Path(__file__).parent
    os.chdir(project_root)
    
    # Add src to Python path
    src_path = project_root / "src"
    if str(src_path) not in sys.path:
        sys.path.insert(0, str(src_path))
    
    success = True
    
    print("🚀 Trading Engine Test Suite")
    print(f"Project root: {project_root}")
    
    # Install dependencies if needed
    if not (project_root / "venv").exists():
        print("\n📦 Installing dependencies...")
        if not run_command("python -m pip install -r requirements.txt", "Installing requirements"):
            print("⚠️  Failed to install requirements, continuing anyway...")
    
    # Linting
    if args.lint or not any([args.unit, args.integration, args.coverage, args.type_check]):
        print("\n🔍 Running linting checks...")
        
        # Check if flake8 is available
        try:
            subprocess.run(["flake8", "--version"], check=True, capture_output=True)
            if not run_command("flake8 src/ tests/ --max-line-length=120 --ignore=E203,W503", "Flake8 linting"):
                success = False
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("⚠️  flake8 not available, skipping linting")
        
        # Check if black is available
        try:
            subprocess.run(["black", "--version"], check=True, capture_output=True)
            if not run_command("black --check src/ tests/", "Black formatting check"):
                print("💡 Run 'black src/ tests/' to fix formatting issues")
                success = False
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("⚠️  black not available, skipping format check")
    
    # Type checking
    if args.type_check or not any([args.unit, args.integration, args.coverage, args.lint]):
        print("\n🔍 Running type checks...")
        try:
            subprocess.run(["mypy", "--version"], check=True, capture_output=True)
            if not run_command("mypy src/ --ignore-missing-imports", "MyPy type checking"):
                success = False
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("⚠️  mypy not available, skipping type checking")
    
    # Prepare pytest command
    pytest_cmd = "python -m pytest"
    
    if args.verbose:
        pytest_cmd += " -v"
    
    if args.fast:
        pytest_cmd += " -m 'not slow'"
    
    if args.coverage:
        pytest_cmd += " --cov=src --cov-report=html --cov-report=term-missing"
    
    # Unit tests
    if args.unit or not any([args.integration, args.coverage]):
        print("\n🧪 Running unit tests...")
        unit_cmd = f"{pytest_cmd} tests/unit/"
        if not run_command(unit_cmd, "Unit tests"):
            success = False
    
    # Integration tests
    if args.integration or not any([args.unit, args.coverage]):
        print("\n🔗 Running integration tests...")
        integration_cmd = f"{pytest_cmd} tests/integration/"
        if not run_command(integration_cmd, "Integration tests"):
            success = False
    
    # Full test suite with coverage
    if args.coverage:
        print("\n📊 Running full test suite with coverage...")
        coverage_cmd = f"{pytest_cmd} tests/"
        if not run_command(coverage_cmd, "Full test suite with coverage"):
            success = False
        
        # Open coverage report
        coverage_html = project_root / "htmlcov" / "index.html"
        if coverage_html.exists():
            print(f"\n📈 Coverage report generated: {coverage_html}")
            print("💡 Open the HTML file in your browser to view detailed coverage")
    
    # Run all tests if no specific option was chosen
    if not any([args.unit, args.integration, args.coverage, args.lint, args.type_check]):
        print("\n🧪 Running all tests...")
        all_tests_cmd = f"{pytest_cmd} tests/"
        if not run_command(all_tests_cmd, "All tests"):
            success = False
    
    # Summary
    print(f"\n{'='*60}")
    if success:
        print("🎉 All tests passed!")
        print("✅ Trading engine is ready for deployment")
    else:
        print("❌ Some tests failed!")
        print("🔧 Please fix the issues before deploying")
    print(f"{'='*60}")
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
