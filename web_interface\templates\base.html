<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Robinhood Trading Engine{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Socket.IO -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    
    <style>
        :root {
            --primary-color: #00d4aa;
            --secondary-color: #1a1a1a;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --dark-bg: #121212;
            --card-bg: #1e1e1e;
        }

        body {
            background-color: var(--dark-bg);
            color: #ffffff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background-color: var(--secondary-color) !important;
            border-bottom: 2px solid var(--primary-color);
        }

        .navbar-brand {
            color: var(--primary-color) !important;
            font-weight: bold;
        }

        .card {
            background-color: var(--card-bg);
            border: 1px solid #333;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        }

        .card-header {
            background-color: var(--secondary-color);
            border-bottom: 1px solid #333;
            color: var(--primary-color);
            font-weight: bold;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: #000;
        }

        .btn-primary:hover {
            background-color: #00b894;
            border-color: #00b894;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-running {
            background-color: var(--success-color);
            animation: pulse 2s infinite;
        }

        .status-stopped {
            background-color: var(--danger-color);
        }

        .status-warning {
            background-color: var(--warning-color);
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .metric-card {
            text-align: center;
            padding: 20px;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 0.9rem;
            color: #aaa;
        }

        .positive {
            color: var(--success-color);
        }

        .negative {
            color: var(--danger-color);
        }

        .neutral {
            color: #fff;
        }

        .table-dark {
            background-color: var(--card-bg);
        }

        .table-dark td, .table-dark th {
            border-color: #333;
        }

        .alert-dark {
            background-color: var(--card-bg);
            border-color: #333;
            color: #fff;
        }

        .connection-status {
            position: fixed;
            top: 70px;
            right: 20px;
            z-index: 1000;
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 0.9rem;
        }

        .connected {
            background-color: var(--success-color);
            color: white;
        }

        .disconnected {
            background-color: var(--danger-color);
            color: white;
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }

        .loading-spinner {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }

        .emergency-stop {
            background-color: var(--danger-color) !important;
            border-color: var(--danger-color) !important;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.5; }
        }

        .strategy-card {
            transition: all 0.3s ease;
        }

        .strategy-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
        }

        .signal-badge {
            font-size: 0.8rem;
            padding: 4px 8px;
        }

        .signal-buy {
            background-color: var(--success-color);
        }

        .signal-sell {
            background-color: var(--danger-color);
        }

        .signal-hold {
            background-color: var(--warning-color);
            color: #000;
        }
    </style>

    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>
                Robinhood Trading Engine
            </a>

            <div class="navbar-nav me-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                </a>
                <a class="nav-link" href="/analytics">
                    <i class="fas fa-chart-bar me-1"></i>Analytics
                </a>
                <a class="nav-link" href="/settings">
                    <i class="fas fa-cog me-1"></i>Settings
                </a>
            </div>

            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3" id="current-time"></span>
                <span class="navbar-text" id="connection-status">
                    <span class="status-indicator status-stopped"></span>
                    Disconnected
                </span>
            </div>
        </div>
    </nav>

    <!-- Connection Status Alert -->
    <div id="connection-alert" class="connection-status disconnected" style="display: none;">
        <i class="fas fa-wifi me-2"></i>
        <span id="connection-text">Disconnected</span>
    </div>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        {% block content %}{% endblock %}
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Base JavaScript -->
    <script>
        // Global variables
        let socket;
        let isConnected = false;
        let lastUpdateTime = null;

        // Initialize Socket.IO connection
        function initializeSocket() {
            socket = io();
            
            socket.on('connect', function() {
                isConnected = true;
                updateConnectionStatus(true);
                console.log('Connected to server');
            });
            
            socket.on('disconnect', function() {
                isConnected = false;
                updateConnectionStatus(false);
                console.log('Disconnected from server');
            });
            
            socket.on('real_time_update', function(data) {
                lastUpdateTime = new Date(data.timestamp);
                handleRealTimeUpdate(data);
            });
        }

        // Update connection status indicator
        function updateConnectionStatus(connected) {
            const statusElement = document.getElementById('connection-status');
            const alertElement = document.getElementById('connection-alert');
            const connectionText = document.getElementById('connection-text');
            
            if (connected) {
                statusElement.innerHTML = '<span class="status-indicator status-running"></span>Connected';
                alertElement.className = 'connection-status connected';
                connectionText.textContent = 'Connected';
                setTimeout(() => alertElement.style.display = 'none', 3000);
            } else {
                statusElement.innerHTML = '<span class="status-indicator status-stopped"></span>Disconnected';
                alertElement.className = 'connection-status disconnected';
                connectionText.textContent = 'Disconnected';
                alertElement.style.display = 'block';
            }
        }

        // Update current time
        function updateCurrentTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleTimeString();
        }

        // Format currency
        function formatCurrency(value) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD'
            }).format(value);
        }

        // Format percentage
        function formatPercentage(value) {
            return new Intl.NumberFormat('en-US', {
                style: 'percent',
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(value / 100);
        }

        // Show loading spinner
        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.innerHTML = '<div class="loading-spinner"><div class="spinner-border text-primary" role="status"></div></div>';
            }
        }

        // Show error message
        function showError(elementId, message) {
            const element = document.getElementById(elementId);
            if (element) {
                element.innerHTML = `<div class="alert alert-danger">${message}</div>`;
            }
        }

        // Make API request
        async function apiRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                return await response.json();
            } catch (error) {
                console.error('API request failed:', error);
                throw error;
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializeSocket();
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);
            
            // Load initial data
            if (typeof loadInitialData === 'function') {
                loadInitialData();
            }
        });

        // Placeholder for real-time update handler (to be overridden in specific pages)
        function handleRealTimeUpdate(data) {
            // Override this function in specific pages
        }
    </script>

    {% block extra_scripts %}{% endblock %}
</body>
</html>
