"""Data downloader utility for fetching historical market data."""

import os
import time
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
import pandas as pd
import robin_stocks.robinhood as rh

from ..utils import get_logger, config


class DataDownloader:
    """Utility for downloading historical market data."""
    
    def __init__(self):
        self.logger = get_logger("trading_engine.data_downloader")
        self.data_dir = config.historical_data_path
        self.data_dir.mkdir(parents=True, exist_ok=True)
    
    def download_robinhood_data(
        self,
        symbols: List[str],
        start_date: datetime,
        end_date: datetime,
        interval: str = '1d',
        save_to_file: bool = True
    ) -> pd.DataFrame:
        """Download historical data from Robinhood.
        
        Args:
            symbols: List of symbols to download
            start_date: Start date
            end_date: End date
            interval: Data interval ('1d', '1h', etc.)
            save_to_file: Whether to save data to file
            
        Returns:
            DataFrame with historical data
        """
        self.logger.info(f"Downloading data for {len(symbols)} symbols from {start_date} to {end_date}")
        
        all_data = []
        
        # Map interval to Robinhood format
        interval_map = {
            '5m': ('5minute', 'week'),
            '15m': ('15minute', 'week'),
            '1h': ('hour', 'month'),
            '1d': ('day', 'year'),
            '1w': ('week', '5year')
        }
        
        rh_interval, rh_span = interval_map.get(interval, ('day', 'year'))
        
        for symbol in symbols:
            try:
                self.logger.info(f"Downloading {symbol}...")
                
                # Get historical data from Robinhood
                historical = rh.stocks.get_stock_historicals(
                    symbol,
                    interval=rh_interval,
                    span=rh_span
                )
                
                if not historical:
                    self.logger.warning(f"No data found for {symbol}")
                    continue
                
                # Convert to DataFrame format
                for bar in historical:
                    timestamp = datetime.fromisoformat(bar['begins_at'].replace('Z', '+00:00'))
                    
                    # Filter by date range
                    if start_date <= timestamp <= end_date:
                        all_data.append({
                            'timestamp': timestamp,
                            'symbol': symbol,
                            'open': float(bar['open_price']),
                            'high': float(bar['high_price']),
                            'low': float(bar['low_price']),
                            'close': float(bar['close_price']),
                            'volume': int(bar['volume'])
                        })
                
                # Rate limiting
                time.sleep(0.5)
                
            except Exception as e:
                self.logger.error(f"Error downloading data for {symbol}: {e}")
                continue
        
        # Create DataFrame
        df = pd.DataFrame(all_data)
        
        if df.empty:
            self.logger.warning("No data downloaded")
            return df
        
        # Sort by timestamp and symbol
        df = df.sort_values(['timestamp', 'symbol']).reset_index(drop=True)
        
        self.logger.info(f"Downloaded {len(df)} data points")
        
        # Save to file if requested
        if save_to_file:
            filename = f"historical_data_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}_{interval}.csv"
            filepath = self.data_dir / filename
            df.to_csv(filepath, index=False)
            self.logger.info(f"Saved data to {filepath}")
        
        return df
    
    def download_sample_data(self, symbols: List[str] = None) -> pd.DataFrame:
        """Download sample data for testing.
        
        Args:
            symbols: List of symbols (defaults to common stocks)
            
        Returns:
            DataFrame with sample data
        """
        if symbols is None:
            symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'SPY']
        
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365)  # 1 year of data
        
        return self.download_robinhood_data(
            symbols=symbols,
            start_date=start_date,
            end_date=end_date,
            interval='1d',
            save_to_file=True
        )
    
    def generate_synthetic_data(
        self,
        symbols: List[str],
        start_date: datetime,
        end_date: datetime,
        initial_prices: Optional[Dict[str, float]] = None
    ) -> pd.DataFrame:
        """Generate synthetic market data for testing.
        
        Args:
            symbols: List of symbols
            start_date: Start date
            end_date: End date
            initial_prices: Initial prices for each symbol
            
        Returns:
            DataFrame with synthetic data
        """
        import numpy as np
        
        self.logger.info(f"Generating synthetic data for {len(symbols)} symbols")
        
        # Generate date range
        dates = pd.date_range(start=start_date, end=end_date, freq='D')
        
        all_data = []
        
        for symbol in symbols:
            # Initial price
            if initial_prices and symbol in initial_prices:
                price = initial_prices[symbol]
            else:
                price = np.random.uniform(50, 200)  # Random initial price
            
            for date in dates:
                # Generate random walk
                daily_return = np.random.normal(0.001, 0.02)  # 0.1% mean, 2% volatility
                price *= (1 + daily_return)
                
                # Generate OHLC from close price
                volatility = np.random.uniform(0.01, 0.03)
                high = price * (1 + volatility)
                low = price * (1 - volatility)
                open_price = np.random.uniform(low, high)
                
                # Generate volume
                volume = int(np.random.uniform(100000, 1000000))
                
                all_data.append({
                    'timestamp': date,
                    'symbol': symbol,
                    'open': round(open_price, 2),
                    'high': round(high, 2),
                    'low': round(low, 2),
                    'close': round(price, 2),
                    'volume': volume
                })
        
        df = pd.DataFrame(all_data)
        df = df.sort_values(['timestamp', 'symbol']).reset_index(drop=True)
        
        # Save to file
        filename = f"synthetic_data_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}.csv"
        filepath = self.data_dir / filename
        df.to_csv(filepath, index=False)
        
        self.logger.info(f"Generated {len(df)} synthetic data points, saved to {filepath}")
        
        return df
    
    def load_data_file(self, filename: str) -> pd.DataFrame:
        """Load data from file.
        
        Args:
            filename: Name of the file to load
            
        Returns:
            DataFrame with loaded data
        """
        filepath = self.data_dir / filename
        
        if not filepath.exists():
            raise FileNotFoundError(f"Data file not found: {filepath}")
        
        if filename.endswith('.csv'):
            df = pd.read_csv(filepath)
        elif filename.endswith('.parquet'):
            df = pd.read_parquet(filepath)
        else:
            raise ValueError(f"Unsupported file format: {filename}")
        
        # Ensure timestamp is datetime
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        self.logger.info(f"Loaded {len(df)} rows from {filepath}")
        
        return df
    
    def list_data_files(self) -> List[str]:
        """List available data files.
        
        Returns:
            List of data file names
        """
        files = []
        for file in self.data_dir.glob('*.csv'):
            files.append(file.name)
        for file in self.data_dir.glob('*.parquet'):
            files.append(file.name)
        
        return sorted(files)
    
    def get_data_info(self, filename: str) -> Dict[str, Any]:
        """Get information about a data file.
        
        Args:
            filename: Name of the file
            
        Returns:
            Dictionary with file information
        """
        try:
            df = self.load_data_file(filename)
            
            info = {
                'filename': filename,
                'rows': len(df),
                'symbols': df['symbol'].nunique() if 'symbol' in df.columns else 0,
                'symbol_list': sorted(df['symbol'].unique().tolist()) if 'symbol' in df.columns else [],
                'date_range': {
                    'start': df['timestamp'].min().isoformat() if 'timestamp' in df.columns else None,
                    'end': df['timestamp'].max().isoformat() if 'timestamp' in df.columns else None
                },
                'columns': df.columns.tolist()
            }
            
            return info
            
        except Exception as e:
            self.logger.error(f"Error getting info for {filename}: {e}")
            return {'filename': filename, 'error': str(e)}
