"""Base strategy interface and data structures."""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any, Union
import pandas as pd

from ..data_feed import MarketData, OHLCV
from ..broker import OrderSide, OrderType


class SignalType(Enum):
    """Signal type enumeration."""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"
    CLOSE = "close"


@dataclass
class Signal:
    """Trading signal data structure."""
    symbol: str
    signal_type: SignalType
    timestamp: datetime
    price: float
    confidence: float = 0.0  # 0.0 to 1.0
    quantity: Optional[int] = None
    order_type: OrderType = OrderType.MARKET
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_order_args(self) -> Dict[str, Any]:
        """Convert signal to order arguments."""
        side = OrderSide.BUY if self.signal_type == SignalType.BUY else OrderSide.SELL
        
        return {
            'symbol': self.symbol,
            'side': side,
            'order_type': self.order_type,
            'quantity': self.quantity or 0,
            'price': self.price if self.order_type == OrderType.LIMIT else None,
            'metadata': self.metadata
        }


@dataclass
class StrategyResult:
    """Strategy execution result."""
    strategy_name: str
    timestamp: datetime
    signals: List[Signal]
    indicators: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def get_signals_for_symbol(self, symbol: str) -> List[Signal]:
        """Get signals for a specific symbol."""
        return [signal for signal in self.signals if signal.symbol == symbol]
    
    def has_signals(self) -> bool:
        """Check if there are any signals."""
        return len(self.signals) > 0


class BaseStrategy(ABC):
    """Abstract base class for trading strategies."""
    
    def __init__(self, name: str, symbols: List[str], parameters: Optional[Dict[str, Any]] = None):
        """Initialize strategy.
        
        Args:
            name: Strategy name
            symbols: List of symbols to trade
            parameters: Strategy parameters
        """
        self.name = name
        self.symbols = symbols
        self.parameters = parameters or {}
        self.is_active = True
        
        # Historical data storage
        self.data_history: Dict[str, pd.DataFrame] = {}
        self.signal_history: List[Signal] = []
        
        # Performance tracking
        self.total_signals = 0
        self.successful_signals = 0
        self.last_update: Optional[datetime] = None
    
    @abstractmethod
    def generate_signals(self, market_data: MarketData) -> StrategyResult:
        """Generate trading signals based on market data.
        
        Args:
            market_data: Current market data
            
        Returns:
            Strategy result with signals and indicators
        """
        pass
    
    @abstractmethod
    def update_data(self, market_data: MarketData):
        """Update internal data with new market data.
        
        Args:
            market_data: New market data
        """
        pass
    
    def add_symbol(self, symbol: str):
        """Add a symbol to the strategy."""
        if symbol not in self.symbols:
            self.symbols.append(symbol)
            self.data_history[symbol] = pd.DataFrame()
    
    def remove_symbol(self, symbol: str):
        """Remove a symbol from the strategy."""
        if symbol in self.symbols:
            self.symbols.remove(symbol)
            if symbol in self.data_history:
                del self.data_history[symbol]
    
    def get_parameter(self, key: str, default: Any = None) -> Any:
        """Get a strategy parameter."""
        return self.parameters.get(key, default)
    
    def set_parameter(self, key: str, value: Any):
        """Set a strategy parameter."""
        self.parameters[key] = value
    
    def get_data_for_symbol(self, symbol: str, lookback: Optional[int] = None) -> pd.DataFrame:
        """Get historical data for a symbol.
        
        Args:
            symbol: Symbol to get data for
            lookback: Number of periods to look back (None for all)
            
        Returns:
            DataFrame with historical data
        """
        if symbol not in self.data_history:
            return pd.DataFrame()
        
        data = self.data_history[symbol]
        if lookback and len(data) > lookback:
            return data.tail(lookback)
        return data
    
    def _update_symbol_data(self, symbol: str, ohlcv: OHLCV):
        """Update historical data for a symbol."""
        if symbol not in self.data_history:
            self.data_history[symbol] = pd.DataFrame()
        
        # Convert OHLCV to DataFrame row
        new_row = pd.DataFrame([{
            'timestamp': ohlcv.timestamp,
            'open': ohlcv.open,
            'high': ohlcv.high,
            'low': ohlcv.low,
            'close': ohlcv.close,
            'volume': ohlcv.volume
        }])
        
        # Append to history
        self.data_history[symbol] = pd.concat([self.data_history[symbol], new_row], ignore_index=True)
        
        # Keep only recent data (e.g., last 1000 periods)
        max_history = self.get_parameter('max_history', 1000)
        if len(self.data_history[symbol]) > max_history:
            self.data_history[symbol] = self.data_history[symbol].tail(max_history).reset_index(drop=True)
    
    def record_signal(self, signal: Signal):
        """Record a signal for tracking."""
        self.signal_history.append(signal)
        self.total_signals += 1
        
        # Keep only recent signals
        max_signal_history = self.get_parameter('max_signal_history', 1000)
        if len(self.signal_history) > max_signal_history:
            self.signal_history = self.signal_history[-max_signal_history:]
    
    def get_recent_signals(self, symbol: Optional[str] = None, count: int = 10) -> List[Signal]:
        """Get recent signals.
        
        Args:
            symbol: Filter by symbol (None for all)
            count: Number of signals to return
            
        Returns:
            List of recent signals
        """
        signals = self.signal_history
        
        if symbol:
            signals = [s for s in signals if s.symbol == symbol]
        
        return signals[-count:] if signals else []
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get strategy performance statistics."""
        return {
            'name': self.name,
            'symbols': self.symbols,
            'total_signals': self.total_signals,
            'successful_signals': self.successful_signals,
            'success_rate': (self.successful_signals / self.total_signals * 100) if self.total_signals > 0 else 0,
            'last_update': self.last_update.isoformat() if self.last_update else None,
            'is_active': self.is_active,
            'parameters': self.parameters
        }
    
    def activate(self):
        """Activate the strategy."""
        self.is_active = True
    
    def deactivate(self):
        """Deactivate the strategy."""
        self.is_active = False
    
    def reset(self):
        """Reset strategy state."""
        self.data_history.clear()
        self.signal_history.clear()
        self.total_signals = 0
        self.successful_signals = 0
        self.last_update = None
        
        # Re-initialize data history for symbols
        for symbol in self.symbols:
            self.data_history[symbol] = pd.DataFrame()
