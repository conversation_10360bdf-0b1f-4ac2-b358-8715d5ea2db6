"""ATR Breakout strategy implementation."""

from datetime import datetime
from typing import Dict, List, Optional, Any
import pandas as pd
import numpy as np

from .base_strategy import BaseStrategy, Signal, SignalType, StrategyResult
from .technical_indicators import TechnicalIndicators
from ..data_feed import MarketData, OHLCV
from ..broker import OrderType
from ..utils import get_logger, config


class ATRBreakoutStrategy(BaseStrategy):
    """ATR-based breakout strategy."""
    
    def __init__(self, symbols: List[str], parameters: Optional[Dict[str, Any]] = None):
        """Initialize ATR Breakout strategy."""
        default_params = {
            'atr_period': config.atr_period,      # 14
            'breakout_multiplier': 2.0,           # ATR multiplier for breakout
            'stop_multiplier': 1.5,               # ATR multiplier for stop loss
            'profit_multiplier': 3.0,             # ATR multiplier for profit target
            'min_volume_ratio': 1.5,              # Minimum volume ratio for confirmation
            'lookback_period': 20,                # Lookback period for range calculation
            'max_history': 200
        }
        
        if parameters:
            default_params.update(parameters)
        
        super().__init__("ATR_Breakout", symbols, default_params)
        self.logger = get_logger("trading_engine.strategy.atr_breakout")
        
        # Track breakout levels
        self.resistance_levels: Dict[str, float] = {}
        self.support_levels: Dict[str, float] = {}
    
    def generate_signals(self, market_data: MarketData) -> StrategyResult:
        """Generate ATR breakout signals."""
        if not self.is_active:
            return StrategyResult(self.name, market_data.timestamp, [])
        
        signals = []
        indicators = {}
        
        for symbol in self.symbols:
            if symbol not in market_data.data:
                continue
            
            try:
                signal = self._analyze_symbol(symbol, market_data.timestamp)
                if signal:
                    signals.append(signal)
                    self.record_signal(signal)
                
                symbol_indicators = self._get_indicators(symbol)
                if symbol_indicators:
                    indicators[symbol] = symbol_indicators
                    
            except Exception as e:
                self.logger.error(f"Error analyzing {symbol}: {e}")
                continue
        
        self.last_update = market_data.timestamp
        
        return StrategyResult(
            strategy_name=self.name,
            timestamp=market_data.timestamp,
            signals=signals,
            indicators=indicators
        )
    
    def update_data(self, market_data: MarketData):
        """Update internal data with new market data."""
        for symbol, ohlcv in market_data.data.items():
            if symbol in self.symbols:
                self._update_symbol_data(symbol, ohlcv)
                self._update_breakout_levels(symbol)
    
    def _analyze_symbol(self, symbol: str, timestamp: datetime) -> Optional[Signal]:
        """Analyze symbol for ATR breakout signals."""
        data = self.get_data_for_symbol(symbol)
        
        min_periods = max(self.get_parameter('atr_period'), self.get_parameter('lookback_period')) + 5
        if len(data) < min_periods:
            return None
        
        current_price = data['close'].iloc[-1]
        current_volume = data['volume'].iloc[-1]
        
        # Get breakout levels
        resistance = self.resistance_levels.get(symbol)
        support = self.support_levels.get(symbol)
        
        if resistance is None or support is None:
            return None
        
        # Check for breakouts
        upward_breakout = current_price > resistance
        downward_breakout = current_price < support
        
        if not (upward_breakout or downward_breakout):
            return None
        
        # Volume confirmation
        avg_volume = data['volume'].tail(20).mean()
        volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1
        
        if volume_ratio < self.get_parameter('min_volume_ratio'):
            return None
        
        # Calculate ATR for position sizing
        atr_value = TechnicalIndicators.atr(
            data['high'], data['low'], data['close'],
            period=self.get_parameter('atr_period')
        ).iloc[-1]
        
        # Generate signal
        if upward_breakout:
            signal_type = SignalType.BUY
            stop_loss = current_price - (atr_value * self.get_parameter('stop_multiplier'))
            take_profit = current_price + (atr_value * self.get_parameter('profit_multiplier'))
        else:
            signal_type = SignalType.SELL
            stop_loss = current_price + (atr_value * self.get_parameter('stop_multiplier'))
            take_profit = current_price - (atr_value * self.get_parameter('profit_multiplier'))
        
        # Calculate confidence based on breakout strength and volume
        breakout_strength = abs(current_price - (resistance if upward_breakout else support)) / atr_value
        confidence = min(0.9, 0.5 + (breakout_strength * 0.1) + (volume_ratio * 0.1))
        
        return Signal(
            symbol=symbol,
            signal_type=signal_type,
            timestamp=timestamp,
            price=current_price,
            confidence=confidence,
            order_type=OrderType.MARKET,
            stop_loss=stop_loss,
            take_profit=take_profit,
            metadata={
                'strategy': 'atr_breakout',
                'breakout_type': 'upward' if upward_breakout else 'downward',
                'resistance_level': resistance,
                'support_level': support,
                'atr': atr_value,
                'volume_ratio': volume_ratio,
                'breakout_strength': breakout_strength
            }
        )
    
    def _update_breakout_levels(self, symbol: str):
        """Update resistance and support levels for a symbol."""
        data = self.get_data_for_symbol(symbol)
        
        lookback = self.get_parameter('lookback_period')
        if len(data) < lookback:
            return
        
        # Get recent data
        recent_data = data.tail(lookback)
        
        # Calculate ATR
        atr_value = TechnicalIndicators.atr(
            recent_data['high'], recent_data['low'], recent_data['close'],
            period=self.get_parameter('atr_period')
        ).iloc[-1]
        
        # Calculate breakout levels
        highest_high = recent_data['high'].max()
        lowest_low = recent_data['low'].min()
        
        breakout_multiplier = self.get_parameter('breakout_multiplier')
        
        self.resistance_levels[symbol] = highest_high + (atr_value * breakout_multiplier)
        self.support_levels[symbol] = lowest_low - (atr_value * breakout_multiplier)
    
    def _get_indicators(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get current indicators for a symbol."""
        data = self.get_data_for_symbol(symbol)
        
        if len(data) < self.get_parameter('atr_period'):
            return None
        
        try:
            # Calculate ATR
            atr_value = TechnicalIndicators.atr(
                data['high'], data['low'], data['close'],
                period=self.get_parameter('atr_period')
            ).iloc[-1]
            
            # Get current levels
            resistance = self.resistance_levels.get(symbol)
            support = self.support_levels.get(symbol)
            current_price = data['close'].iloc[-1]
            
            # Calculate distance to levels
            distance_to_resistance = (resistance - current_price) / current_price * 100 if resistance else None
            distance_to_support = (current_price - support) / current_price * 100 if support else None
            
            return {
                'atr': atr_value,
                'resistance_level': resistance,
                'support_level': support,
                'current_price': current_price,
                'distance_to_resistance_pct': distance_to_resistance,
                'distance_to_support_pct': distance_to_support,
                'recent_high': data['high'].tail(20).max(),
                'recent_low': data['low'].tail(20).min(),
                'avg_volume': data['volume'].tail(20).mean()
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating indicators for {symbol}: {e}")
            return None
