version: 1
disable_existing_loggers: false

formatters:
  standard:
    format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
    datefmt: "%Y-%m-%d %H:%M:%S"
  
  detailed:
    format: "%(asctime)s [%(levelname)s] %(name)s:%(lineno)d - %(funcName)s(): %(message)s"
    datefmt: "%Y-%m-%d %H:%M:%S"
  
  json:
    format: '{"timestamp": "%(asctime)s", "level": "%(levelname)s", "logger": "%(name)s", "message": "%(message)s", "module": "%(module)s", "function": "%(funcName)s", "line": %(lineno)d}'
    datefmt: "%Y-%m-%d %H:%M:%S"

handlers:
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: standard
    stream: ext://sys.stdout
  
  file:
    class: logging.handlers.RotatingFileHandler
    level: DEBUG
    formatter: detailed
    filename: logs/trading_engine.log
    maxBytes: 10485760  # 10MB
    backupCount: 5
    encoding: utf8
  
  error_file:
    class: logging.handlers.RotatingFileHandler
    level: ERROR
    formatter: json
    filename: logs/errors.log
    maxBytes: 10485760  # 10MB
    backupCount: 3
    encoding: utf8
  
  trades_file:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: json
    filename: logs/trades.log
    maxBytes: 10485760  # 10MB
    backupCount: 10
    encoding: utf8

loggers:
  trading_engine:
    level: DEBUG
    handlers: [console, file, error_file]
    propagate: false
  
  trading_engine.broker:
    level: DEBUG
    handlers: [console, file]
    propagate: false
  
  trading_engine.strategy:
    level: INFO
    handlers: [console, file]
    propagate: false
  
  trading_engine.risk:
    level: INFO
    handlers: [console, file, error_file]
    propagate: false
  
  trading_engine.trades:
    level: INFO
    handlers: [trades_file]
    propagate: false
  
  robin_stocks:
    level: WARNING
    handlers: [console, file]
    propagate: false

root:
  level: INFO
  handlers: [console, file]
