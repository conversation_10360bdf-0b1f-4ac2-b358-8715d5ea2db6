"""Position sizing algorithms for risk management."""

import math
from enum import Enum
from typing import Dict, Optional, Any
import numpy as np

from ..utils import get_logger, config, calculate_position_size


class SizingMethod(Enum):
    """Position sizing methods."""
    FIXED_DOLLAR = "fixed_dollar"
    FIXED_PERCENT = "fixed_percent"
    KELLY_CRITERION = "kelly_criterion"
    ATR_BASED = "atr_based"
    VOLATILITY_ADJUSTED = "volatility_adjusted"


class PositionSizer:
    """Position sizing calculator with multiple methods."""
    
    def __init__(self):
        self.logger = get_logger("trading_engine.risk.position_sizer")
        
        # Default parameters
        self.default_risk_per_trade = config.max_position_size  # 2%
        self.max_position_value = None
        self.min_position_size = 1
        self.max_position_size = 10000
    
    def calculate_position_size(
        self,
        account_value: float,
        entry_price: float,
        stop_loss_price: float,
        method: SizingMethod = SizingMethod.FIXED_PERCENT,
        confidence: float = 0.5,
        volatility: Optional[float] = None,
        **kwargs
    ) -> int:
        """Calculate position size based on specified method.
        
        Args:
            account_value: Total account value
            entry_price: Entry price per share
            stop_loss_price: Stop loss price per share
            method: Sizing method to use
            confidence: Signal confidence (0-1)
            volatility: Asset volatility (for volatility-adjusted sizing)
            **kwargs: Additional parameters for specific methods
            
        Returns:
            Number of shares to trade
        """
        try:
            if method == SizingMethod.FIXED_DOLLAR:
                return self._fixed_dollar_sizing(
                    account_value, entry_price, stop_loss_price, **kwargs
                )
            elif method == SizingMethod.FIXED_PERCENT:
                return self._fixed_percent_sizing(
                    account_value, entry_price, stop_loss_price, **kwargs
                )
            elif method == SizingMethod.KELLY_CRITERION:
                return self._kelly_criterion_sizing(
                    account_value, entry_price, stop_loss_price, confidence, **kwargs
                )
            elif method == SizingMethod.ATR_BASED:
                return self._atr_based_sizing(
                    account_value, entry_price, stop_loss_price, **kwargs
                )
            elif method == SizingMethod.VOLATILITY_ADJUSTED:
                return self._volatility_adjusted_sizing(
                    account_value, entry_price, stop_loss_price, volatility, **kwargs
                )
            else:
                self.logger.warning(f"Unknown sizing method: {method}, using fixed percent")
                return self._fixed_percent_sizing(account_value, entry_price, stop_loss_price)
                
        except Exception as e:
            self.logger.error(f"Error calculating position size: {e}")
            return 0
    
    def _fixed_dollar_sizing(
        self,
        account_value: float,
        entry_price: float,
        stop_loss_price: float,
        fixed_amount: float = 1000.0,
        **kwargs
    ) -> int:
        """Fixed dollar amount position sizing."""
        if entry_price <= 0:
            return 0
        
        shares = int(fixed_amount / entry_price)
        return self._apply_constraints(shares, account_value, entry_price)
    
    def _fixed_percent_sizing(
        self,
        account_value: float,
        entry_price: float,
        stop_loss_price: float,
        risk_percent: Optional[float] = None,
        **kwargs
    ) -> int:
        """Fixed percentage risk position sizing."""
        risk_percent = risk_percent or self.default_risk_per_trade
        
        shares = calculate_position_size(
            account_value=account_value,
            risk_per_trade=risk_percent,
            entry_price=entry_price,
            stop_loss_price=stop_loss_price,
            max_position_value=self.max_position_value
        )
        
        return self._apply_constraints(shares, account_value, entry_price)
    
    def _kelly_criterion_sizing(
        self,
        account_value: float,
        entry_price: float,
        stop_loss_price: float,
        confidence: float,
        win_rate: float = 0.55,
        avg_win: float = 1.5,
        avg_loss: float = 1.0,
        **kwargs
    ) -> int:
        """Kelly Criterion position sizing.
        
        Args:
            win_rate: Historical win rate (0-1)
            avg_win: Average win as multiple of risk
            avg_loss: Average loss as multiple of risk
            confidence: Signal confidence (0-1)
        """
        if avg_loss <= 0:
            return 0
        
        # Adjust win rate based on confidence
        adjusted_win_rate = win_rate * confidence
        
        # Kelly formula: f = (bp - q) / b
        # where b = avg_win/avg_loss, p = win_rate, q = 1 - win_rate
        b = avg_win / avg_loss
        p = adjusted_win_rate
        q = 1 - p
        
        kelly_fraction = (b * p - q) / b
        
        # Cap Kelly fraction to prevent over-leveraging
        kelly_fraction = max(0, min(kelly_fraction, 0.25))  # Max 25% of capital
        
        # Calculate position size
        risk_amount = account_value * kelly_fraction
        
        if abs(entry_price - stop_loss_price) <= 0:
            return 0
        
        shares = int(risk_amount / abs(entry_price - stop_loss_price))
        
        return self._apply_constraints(shares, account_value, entry_price)
    
    def _atr_based_sizing(
        self,
        account_value: float,
        entry_price: float,
        stop_loss_price: float,
        atr_value: float,
        atr_multiplier: float = 2.0,
        **kwargs
    ) -> int:
        """ATR-based position sizing."""
        if not atr_value or atr_value <= 0:
            return self._fixed_percent_sizing(account_value, entry_price, stop_loss_price)
        
        # Use ATR to determine risk per share
        risk_per_share = atr_value * atr_multiplier
        risk_amount = account_value * self.default_risk_per_trade
        
        shares = int(risk_amount / risk_per_share)
        
        return self._apply_constraints(shares, account_value, entry_price)
    
    def _volatility_adjusted_sizing(
        self,
        account_value: float,
        entry_price: float,
        stop_loss_price: float,
        volatility: Optional[float],
        base_volatility: float = 0.20,  # 20% base volatility
        **kwargs
    ) -> int:
        """Volatility-adjusted position sizing."""
        if not volatility or volatility <= 0:
            volatility = base_volatility
        
        # Adjust position size inversely to volatility
        volatility_adjustment = base_volatility / volatility
        
        # Cap adjustment to prevent extreme positions
        volatility_adjustment = max(0.5, min(volatility_adjustment, 2.0))
        
        # Calculate base position size
        base_shares = self._fixed_percent_sizing(account_value, entry_price, stop_loss_price)
        
        # Apply volatility adjustment
        adjusted_shares = int(base_shares * volatility_adjustment)
        
        return self._apply_constraints(adjusted_shares, account_value, entry_price)
    
    def _apply_constraints(self, shares: int, account_value: float, entry_price: float) -> int:
        """Apply position size constraints."""
        # Minimum position size
        shares = max(shares, self.min_position_size)
        
        # Maximum position size
        shares = min(shares, self.max_position_size)
        
        # Maximum position value constraint
        if self.max_position_value:
            max_shares_by_value = int(self.max_position_value / entry_price)
            shares = min(shares, max_shares_by_value)
        
        # Maximum percentage of account constraint
        max_position_percent = config.max_concurrent_notional  # 10%
        max_position_value = account_value * max_position_percent
        max_shares_by_percent = int(max_position_value / entry_price)
        shares = min(shares, max_shares_by_percent)
        
        return max(0, shares)
    
    def calculate_risk_amount(
        self,
        shares: int,
        entry_price: float,
        stop_loss_price: float
    ) -> float:
        """Calculate total risk amount for a position.
        
        Args:
            shares: Number of shares
            entry_price: Entry price per share
            stop_loss_price: Stop loss price per share
            
        Returns:
            Total risk amount in dollars
        """
        return shares * abs(entry_price - stop_loss_price)
    
    def calculate_position_value(self, shares: int, price: float) -> float:
        """Calculate total position value.
        
        Args:
            shares: Number of shares
            price: Price per share
            
        Returns:
            Total position value
        """
        return shares * price
    
    def get_sizing_recommendation(
        self,
        account_value: float,
        entry_price: float,
        stop_loss_price: float,
        confidence: float = 0.5,
        volatility: Optional[float] = None,
        atr_value: Optional[float] = None
    ) -> Dict[str, Any]:
        """Get position sizing recommendations using multiple methods.
        
        Returns:
            Dictionary with recommendations from different methods
        """
        recommendations = {}
        
        methods = [
            SizingMethod.FIXED_PERCENT,
            SizingMethod.KELLY_CRITERION,
            SizingMethod.VOLATILITY_ADJUSTED
        ]
        
        if atr_value:
            methods.append(SizingMethod.ATR_BASED)
        
        for method in methods:
            try:
                shares = self.calculate_position_size(
                    account_value=account_value,
                    entry_price=entry_price,
                    stop_loss_price=stop_loss_price,
                    method=method,
                    confidence=confidence,
                    volatility=volatility,
                    atr_value=atr_value
                )
                
                risk_amount = self.calculate_risk_amount(shares, entry_price, stop_loss_price)
                position_value = self.calculate_position_value(shares, entry_price)
                
                recommendations[method.value] = {
                    'shares': shares,
                    'position_value': position_value,
                    'risk_amount': risk_amount,
                    'risk_percent': (risk_amount / account_value * 100) if account_value > 0 else 0
                }
                
            except Exception as e:
                self.logger.error(f"Error calculating {method.value} sizing: {e}")
                continue
        
        return recommendations
