[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --strict-markers
    --strict-config
    --disable-warnings
    -ra
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    broker: marks tests related to broker functionality
    strategy: marks tests related to strategy functionality
    risk: marks tests related to risk management
    data: marks tests related to data feeds
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
