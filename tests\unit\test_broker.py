"""Unit tests for broker components."""

import pytest
from datetime import datetime
from src.broker import PaperBroker, Order, Position, OrderSide, OrderType, OrderStatus


class TestPaperBroker:
    """Test cases for PaperBroker."""
    
    def test_initialization(self):
        """Test broker initialization."""
        broker = PaperBroker(initial_capital=10000.0)
        
        assert broker.initial_capital == 10000.0
        assert broker.cash_balance == 10000.0
        assert len(broker.positions) == 0
        assert not broker.is_connected()
    
    def test_connection(self):
        """Test broker connection."""
        broker = PaperBroker()
        
        assert broker.connect()
        assert broker.is_connected()
        
        broker.disconnect()
        assert not broker.is_connected()
    
    def test_place_buy_order(self, paper_broker, sample_market_data):
        """Test placing a buy order."""
        # Update broker with market data
        paper_broker.update_market_data(
            sample_market_data.timestamp,
            {symbol: ohlcv.to_dict() for symbol, ohlcv in sample_market_data.data.items()}
        )
        
        # Place buy order
        order = paper_broker.place_order(
            symbol='AAPL',
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=100
        )
        
        assert order.symbol == 'AAPL'
        assert order.side == OrderSide.BUY
        assert order.quantity == 100
        assert order.status == OrderStatus.QUEUED
        
        # Process pending orders
        paper_broker._process_pending_orders()
        
        # Check if order was filled
        assert order.status == OrderStatus.FILLED
        assert order.filled_quantity == 100
        
        # Check position was created
        position = paper_broker.get_position('AAPL')
        assert position is not None
        assert position.quantity == 100
    
    def test_place_sell_order_insufficient_shares(self, paper_broker):
        """Test placing sell order with insufficient shares."""
        order = paper_broker.place_order(
            symbol='AAPL',
            side=OrderSide.SELL,
            order_type=OrderType.MARKET,
            quantity=100
        )
        
        assert order.status == OrderStatus.REJECTED
    
    def test_insufficient_buying_power(self, paper_broker, sample_market_data):
        """Test order rejection due to insufficient buying power."""
        # Update broker with market data
        paper_broker.update_market_data(
            sample_market_data.timestamp,
            {symbol: ohlcv.to_dict() for symbol, ohlcv in sample_market_data.data.items()}
        )
        
        # Try to buy more than we can afford
        order = paper_broker.place_order(
            symbol='AAPL',
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=1000  # $151,000 worth at $151/share
        )
        
        assert order.status == OrderStatus.REJECTED
    
    def test_portfolio_value_calculation(self, paper_broker, sample_market_data):
        """Test portfolio value calculation."""
        # Initial portfolio value should equal cash
        initial_value = paper_broker.get_portfolio_value()
        assert initial_value == paper_broker.cash_balance
        
        # Update market data and buy shares
        paper_broker.update_market_data(
            sample_market_data.timestamp,
            {symbol: ohlcv.to_dict() for symbol, ohlcv in sample_market_data.data.items()}
        )
        
        # Buy 100 shares of AAPL at $151
        order = paper_broker.place_order(
            symbol='AAPL',
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=100
        )
        
        paper_broker._process_pending_orders()
        
        # Portfolio value should be cash + position value
        portfolio_value = paper_broker.get_portfolio_value()
        expected_value = paper_broker.cash_balance + (100 * 151.0)
        
        assert abs(portfolio_value - expected_value) < 1.0  # Allow for small rounding differences
    
    def test_order_cancellation(self, paper_broker):
        """Test order cancellation."""
        order = paper_broker.place_order(
            symbol='AAPL',
            side=OrderSide.BUY,
            order_type=OrderType.LIMIT,
            quantity=100,
            price=100.0  # Below market price, won't execute immediately
        )
        
        assert order.status == OrderStatus.QUEUED
        
        # Cancel the order
        success = paper_broker.cancel_order(order.id)
        assert success
        assert order.status == OrderStatus.CANCELLED
    
    def test_performance_stats(self, paper_broker, sample_market_data):
        """Test performance statistics calculation."""
        # Update market data
        paper_broker.update_market_data(
            sample_market_data.timestamp,
            {symbol: ohlcv.to_dict() for symbol, ohlcv in sample_market_data.data.items()}
        )
        
        # Make some trades
        buy_order = paper_broker.place_order(
            symbol='AAPL',
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=100
        )
        
        paper_broker._process_pending_orders()
        
        # Get performance stats
        stats = paper_broker.get_performance_stats()
        
        assert 'initial_capital' in stats
        assert 'current_value' in stats
        assert 'total_return_pct' in stats
        assert 'total_trades' in stats
        assert stats['total_trades'] == 1
        assert stats['initial_capital'] == 10000.0


class TestOrder:
    """Test cases for Order class."""
    
    def test_order_creation(self):
        """Test order creation."""
        order = Order(
            id="test-123",
            symbol="AAPL",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=100
        )
        
        assert order.symbol == "AAPL"
        assert order.side == OrderSide.BUY
        assert order.quantity == 100
        assert order.filled_quantity == 0
        assert order.remaining_quantity == 100
        assert not order.is_filled
        assert order.is_active
    
    def test_order_properties(self):
        """Test order properties."""
        order = Order(
            id="test-123",
            symbol="AAPL",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=100,
            status=OrderStatus.FILLED,
            filled_quantity=100
        )
        
        assert order.is_filled
        assert not order.is_active
        assert order.remaining_quantity == 0


class TestPosition:
    """Test cases for Position class."""
    
    def test_position_creation(self):
        """Test position creation."""
        position = Position(
            symbol="AAPL",
            quantity=100,
            average_price=150.0,
            market_price=151.0
        )
        
        assert position.symbol == "AAPL"
        assert position.quantity == 100
        assert position.average_price == 150.0
        assert position.market_price == 151.0
        assert position.market_value == 15100.0  # 100 * 151
        assert position.cost_basis == 15000.0    # 100 * 150
        assert position.unrealized_pnl == 100.0  # (151 - 150) * 100
    
    def test_position_update(self):
        """Test position market price update."""
        position = Position(
            symbol="AAPL",
            quantity=100,
            average_price=150.0,
            market_price=150.0
        )
        
        # Update market price
        position.update_market_price(155.0)
        
        assert position.market_price == 155.0
        assert position.unrealized_pnl == 500.0  # (155 - 150) * 100
