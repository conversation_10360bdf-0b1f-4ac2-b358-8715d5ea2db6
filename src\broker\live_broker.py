"""Live broker implementation using robin_stocks."""

import time
import pyotp
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
import robin_stocks.robinhood as rh

from .base_broker import BaseBroker, Order, Position, OrderStatus, OrderType, OrderSide
from ..utils import get_logger, config


class LiveBroker(BaseBroker):
    """Live broker implementation using Robinhood API."""
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger("trading_engine.broker.live")
        self._connected = False
        self._last_login = None
        self._login_timeout = timedelta(hours=23)  # Re-login every 23 hours
        
        # Safety checks
        if not config.is_live_mode():
            raise ValueError("LiveBroker can only be used in live mode")
        
        if not config.validate_credentials():
            raise ValueError("Missing required credentials for live trading")
    
    def connect(self) -> bool:
        """Connect to Robinhood."""
        try:
            self.logger.info("Connecting to Robinhood...")
            
            # Generate TOTP if MFA secret is provided
            totp_code = None
            if config.rh_mfa_secret:
                totp = pyotp.TOTP(config.rh_mfa_secret)
                totp_code = totp.now()
                self.logger.debug("Generated TOTP code for MFA")
            
            # Login to Robinhood
            login_result = rh.login(
                username=config.rh_username,
                password=config.rh_password,
                mfa_code=totp_code
            )
            
            if login_result:
                self._connected = True
                self._last_login = datetime.now()
                self.logger.info("Successfully connected to Robinhood")
                
                # Load account information
                self._load_account_info()
                self._load_positions()
                
                return True
            else:
                self.logger.error("Failed to login to Robinhood")
                return False
                
        except Exception as e:
            self.logger.error(f"Error connecting to Robinhood: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from Robinhood."""
        try:
            if self._connected:
                rh.logout()
                self._connected = False
                self.logger.info("Disconnected from Robinhood")
        except Exception as e:
            self.logger.error(f"Error disconnecting from Robinhood: {e}")
    
    def is_connected(self) -> bool:
        """Check if connected and re-login if needed."""
        if not self._connected:
            return False
        
        # Check if we need to re-login
        if self._last_login and datetime.now() - self._last_login > self._login_timeout:
            self.logger.info("Login timeout reached, reconnecting...")
            self.disconnect()
            return self.connect()
        
        return True
    
    def _load_account_info(self):
        """Load account information from Robinhood."""
        try:
            account = rh.profiles.load_account_profile()
            if account:
                self.cash_balance = float(account.get('buying_power', 0))
                self.initial_capital = float(account.get('portfolio_cash', 0))
                self.logger.info(f"Loaded account info: Cash=${self.cash_balance:.2f}")
        except Exception as e:
            self.logger.error(f"Error loading account info: {e}")
    
    def _load_positions(self):
        """Load current positions from Robinhood."""
        try:
            positions_data = rh.positions.get_open_stock_positions()
            self.positions.clear()
            
            for pos_data in positions_data:
                if float(pos_data['quantity']) == 0:
                    continue
                
                instrument_url = pos_data['instrument']
                instrument = rh.stocks.get_instrument_by_url(instrument_url)
                symbol = instrument['symbol']
                
                quantity = int(float(pos_data['quantity']))
                avg_price = float(pos_data['average_buy_price'] or 0)
                
                # Get current market price
                quote = self.get_quote(symbol)
                market_price = quote.get('last_trade_price', avg_price)
                
                position = Position(
                    symbol=symbol,
                    quantity=quantity,
                    average_price=avg_price,
                    market_price=market_price
                )
                
                self.positions[symbol] = position
                self.logger.info(f"Loaded position: {symbol} {quantity} shares @ ${avg_price:.2f}")
                
        except Exception as e:
            self.logger.error(f"Error loading positions: {e}")
    
    def get_account_info(self) -> Dict[str, Any]:
        """Get account information."""
        if not self.is_connected():
            return {}
        
        try:
            account = rh.profiles.load_account_profile()
            portfolio = rh.profiles.load_portfolio_profile()
            
            return {
                'account_number': account.get('account_number'),
                'buying_power': float(account.get('buying_power', 0)),
                'cash': float(account.get('cash', 0)),
                'portfolio_value': float(portfolio.get('total_return_today', 0)),
                'day_trade_count': int(account.get('day_trade_buying_power_held', 0)),
                'pattern_day_trader': account.get('is_day_trader', False)
            }
        except Exception as e:
            self.logger.error(f"Error getting account info: {e}")
            return {}
    
    def get_quote(self, symbol: str) -> Dict[str, Any]:
        """Get current quote for a symbol."""
        if not self.is_connected():
            return {}
        
        try:
            quote = rh.stocks.get_latest_price(symbol, includeExtendedHours=True)
            if quote and len(quote) > 0:
                price = float(quote[0])
                
                # Get additional quote data
                quote_data = rh.stocks.get_quotes(symbol)
                if quote_data and len(quote_data) > 0:
                    data = quote_data[0]
                    return {
                        'symbol': symbol,
                        'last_trade_price': price,
                        'bid_price': float(data.get('bid_price', 0)),
                        'ask_price': float(data.get('ask_price', 0)),
                        'bid_size': int(data.get('bid_size', 0)),
                        'ask_size': int(data.get('ask_size', 0)),
                        'volume': int(data.get('volume', 0)),
                        'updated_at': data.get('updated_at')
                    }
                else:
                    return {'symbol': symbol, 'last_trade_price': price}
            
            return {}
        except Exception as e:
            self.logger.error(f"Error getting quote for {symbol}: {e}")
            return {}

    def place_order(
        self,
        symbol: str,
        side: OrderSide,
        order_type: OrderType,
        quantity: int,
        price: Optional[float] = None,
        stop_price: Optional[float] = None,
        **kwargs
    ) -> Order:
        """Place an order."""
        if not self.is_connected():
            raise RuntimeError("Not connected to broker")

        # Safety check for live trading
        if not config.live_trading_enabled:
            raise RuntimeError("Live trading is disabled in configuration")

        # Validate order value
        estimated_value = quantity * (price or self.get_quote(symbol).get('last_trade_price', 0))
        if estimated_value > config.max_order_value:
            raise ValueError(f"Order value ${estimated_value:.2f} exceeds maximum ${config.max_order_value:.2f}")

        try:
            # Create order object
            order = Order(
                symbol=symbol,
                side=side,
                order_type=order_type,
                quantity=quantity,
                price=price,
                stop_price=stop_price,
                status=OrderStatus.PENDING
            )

            # Place order via Robinhood API
            if side == OrderSide.BUY:
                if order_type == OrderType.MARKET:
                    result = rh.orders.order_buy_market(symbol, quantity)
                elif order_type == OrderType.LIMIT:
                    result = rh.orders.order_buy_limit(symbol, quantity, price)
                else:
                    raise ValueError(f"Unsupported order type: {order_type}")
            else:  # SELL
                if order_type == OrderType.MARKET:
                    result = rh.orders.order_sell_market(symbol, quantity)
                elif order_type == OrderType.LIMIT:
                    result = rh.orders.order_sell_limit(symbol, quantity, price)
                else:
                    raise ValueError(f"Unsupported order type: {order_type}")

            if result and 'id' in result:
                order.id = result['id']
                order.status = OrderStatus.QUEUED
                self.orders[order.id] = order

                self.logger.info(f"Placed order: {side.value} {quantity} {symbol} @ {price or 'market'}")
                return order
            else:
                order.status = OrderStatus.REJECTED
                self.logger.error(f"Order rejected: {result}")
                return order

        except Exception as e:
            self.logger.error(f"Error placing order: {e}")
            order.status = OrderStatus.FAILED
            return order

    def cancel_order(self, order_id: str) -> bool:
        """Cancel an order."""
        if not self.is_connected():
            return False

        try:
            result = rh.orders.cancel_stock_order(order_id)
            if result:
                if order_id in self.orders:
                    self.orders[order_id].status = OrderStatus.CANCELLED
                self.logger.info(f"Cancelled order: {order_id}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"Error cancelling order {order_id}: {e}")
            return False

    def get_order_status(self, order_id: str) -> Optional[Order]:
        """Get order status."""
        if not self.is_connected():
            return None

        try:
            # Get order from Robinhood
            order_data = rh.orders.get_stock_order_info(order_id)
            if not order_data:
                return None

            # Update local order if it exists
            if order_id in self.orders:
                order = self.orders[order_id]

                # Map Robinhood status to our status
                rh_state = order_data.get('state', '').lower()
                if rh_state == 'filled':
                    order.status = OrderStatus.FILLED
                    order.filled_quantity = int(float(order_data.get('quantity', 0)))
                    order.filled_price = float(order_data.get('price', 0))
                elif rh_state == 'cancelled':
                    order.status = OrderStatus.CANCELLED
                elif rh_state == 'rejected':
                    order.status = OrderStatus.REJECTED
                elif rh_state in ['queued', 'confirmed']:
                    order.status = OrderStatus.CONFIRMED

                order.updated_at = datetime.now()
                return order

            return None
        except Exception as e:
            self.logger.error(f"Error getting order status for {order_id}: {e}")
            return None

    def get_positions(self) -> Dict[str, Position]:
        """Get all positions."""
        if self.is_connected():
            self._load_positions()
        return self.positions.copy()

    def get_position(self, symbol: str) -> Optional[Position]:
        """Get position for a specific symbol."""
        return self.positions.get(symbol)
