version: '3.8'

services:
  trading-engine-web:
    build: .
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - FLASK_DEBUG=false
      - FLASK_HOST=0.0.0.0
      - FLASK_PORT=5000
    volumes:
      - ../config:/app/config:ro
      - ../data:/app/data
      - ../logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - trading-network

networks:
  trading-network:
    driver: bridge
