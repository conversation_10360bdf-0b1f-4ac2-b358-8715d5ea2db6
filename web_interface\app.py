"""Flask web application for trading engine interface."""

import os
import sys
import json
import threading
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional

from flask import Flask, render_template, jsonify, request, session
from flask_socketio import SocketIO, emit
from flask_cors import CORS

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.orchestrator import TradingOrchestrator
from src.utils import get_logger, config
from src.data_feed.data_downloader import DataDownloader


class TradingEngineAPI:
    """API wrapper for the trading engine."""
    
    def __init__(self):
        self.logger = get_logger("trading_engine.web_api")
        self.orchestrator: Optional[TradingOrchestrator] = None
        self.symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'SPY']
        self._lock = threading.Lock()
        
    def initialize_engine(self, symbols: list = None) -> Dict[str, Any]:
        """Initialize the trading engine."""
        try:
            with self._lock:
                if self.orchestrator:
                    self.orchestrator.stop()
                
                self.symbols = symbols or self.symbols
                self.orchestrator = TradingOrchestrator(self.symbols)
                
                success = self.orchestrator.initialize()
                
                return {
                    'success': success,
                    'message': 'Engine initialized successfully' if success else 'Failed to initialize engine',
                    'symbols': self.symbols,
                    'mode': config.mode
                }
        except Exception as e:
            self.logger.error(f"Error initializing engine: {e}")
            return {'success': False, 'message': str(e)}
    
    def start_trading(self) -> Dict[str, Any]:
        """Start the trading engine."""
        try:
            with self._lock:
                if not self.orchestrator:
                    return {'success': False, 'message': 'Engine not initialized'}
                
                success = self.orchestrator.start()
                
                return {
                    'success': success,
                    'message': 'Trading started successfully' if success else 'Failed to start trading'
                }
        except Exception as e:
            self.logger.error(f"Error starting trading: {e}")
            return {'success': False, 'message': str(e)}
    
    def stop_trading(self) -> Dict[str, Any]:
        """Stop the trading engine."""
        try:
            with self._lock:
                if not self.orchestrator:
                    return {'success': False, 'message': 'Engine not initialized'}
                
                self.orchestrator.stop()
                
                return {
                    'success': True,
                    'message': 'Trading stopped successfully'
                }
        except Exception as e:
            self.logger.error(f"Error stopping trading: {e}")
            return {'success': False, 'message': str(e)}
    
    def get_status(self) -> Dict[str, Any]:
        """Get current engine status."""
        try:
            if not self.orchestrator:
                return {
                    'initialized': False,
                    'running': False,
                    'message': 'Engine not initialized'
                }
            
            status = self.orchestrator.get_status()
            status['initialized'] = True
            
            return status
        except Exception as e:
            self.logger.error(f"Error getting status: {e}")
            return {'error': str(e)}
    
    def get_portfolio(self) -> Dict[str, Any]:
        """Get portfolio information."""
        try:
            if not self.orchestrator or not self.orchestrator.broker:
                return {'error': 'Engine not initialized'}
            
            broker = self.orchestrator.broker
            positions = broker.get_positions()
            
            portfolio_data = {
                'total_value': broker.get_portfolio_value(),
                'cash_balance': broker.get_cash_balance(),
                'buying_power': broker.get_buying_power(),
                'positions': []
            }
            
            for symbol, position in positions.items():
                portfolio_data['positions'].append({
                    'symbol': symbol,
                    'quantity': position.quantity,
                    'average_price': position.average_price,
                    'market_price': position.market_price,
                    'market_value': position.market_value,
                    'unrealized_pnl': position.unrealized_pnl,
                    'unrealized_pnl_pct': (position.unrealized_pnl / position.cost_basis * 100) if position.cost_basis > 0 else 0
                })
            
            # Add performance stats if available
            if hasattr(broker, 'get_performance_stats'):
                portfolio_data['performance'] = broker.get_performance_stats()
            
            return portfolio_data
        except Exception as e:
            self.logger.error(f"Error getting portfolio: {e}")
            return {'error': str(e)}
    
    def get_strategies(self) -> Dict[str, Any]:
        """Get strategy information."""
        try:
            if not self.orchestrator or not self.orchestrator.strategy_engine:
                return {'error': 'Engine not initialized'}
            
            engine = self.orchestrator.strategy_engine
            strategies_data = {
                'strategies': [],
                'performance': engine.get_performance_stats()
            }
            
            for name in engine.list_strategies():
                strategy = engine.get_strategy(name)
                if strategy:
                    strategies_data['strategies'].append({
                        'name': name,
                        'is_active': strategy.is_active,
                        'symbols': strategy.symbols,
                        'total_signals': strategy.total_signals,
                        'successful_signals': strategy.successful_signals,
                        'success_rate': (strategy.successful_signals / strategy.total_signals * 100) if strategy.total_signals > 0 else 0,
                        'parameters': strategy.parameters
                    })
            
            # Get recent signals
            recent_signals = engine.get_recent_signals(count=10)
            strategies_data['recent_signals'] = [
                {
                    'symbol': signal.symbol,
                    'signal_type': signal.signal_type.value,
                    'timestamp': signal.timestamp.isoformat(),
                    'price': signal.price,
                    'confidence': signal.confidence,
                    'quantity': signal.quantity,
                    'metadata': signal.metadata
                }
                for signal in recent_signals
            ]
            
            return strategies_data
        except Exception as e:
            self.logger.error(f"Error getting strategies: {e}")
            return {'error': str(e)}
    
    def get_risk_status(self) -> Dict[str, Any]:
        """Get risk management status."""
        try:
            if not self.orchestrator or not self.orchestrator.risk_manager:
                return {'error': 'Engine not initialized'}
            
            risk_manager = self.orchestrator.risk_manager
            risk_status = risk_manager.get_risk_status()
            
            # Add stop loss information
            stop_manager = risk_manager.stop_loss_manager
            risk_status['stop_losses'] = []
            
            for symbol, stop_order in stop_manager.stop_orders.items():
                risk_status['stop_losses'].append({
                    'symbol': symbol,
                    'stop_type': stop_order.stop_type.value,
                    'stop_price': stop_order.stop_price,
                    'original_stop': stop_order.original_stop,
                    'is_active': stop_order.is_active,
                    'entry_price': stop_order.entry_price,
                    'entry_time': stop_order.entry_time.isoformat()
                })
            
            return risk_status
        except Exception as e:
            self.logger.error(f"Error getting risk status: {e}")
            return {'error': str(e)}


# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'trading-engine-secret-key'
CORS(app)
socketio = SocketIO(app, cors_allowed_origins="*")

# Initialize API
api = TradingEngineAPI()


@app.route('/')
def index():
    """Main dashboard page."""
    return render_template('dashboard.html')


@app.route('/analytics')
def analytics():
    """Analytics page."""
    return render_template('analytics.html')


@app.route('/settings')
def settings():
    """Settings page."""
    return render_template('settings.html')


@app.route('/api/status')
def get_status():
    """Get engine status."""
    return jsonify(api.get_status())


@app.route('/api/portfolio')
def get_portfolio():
    """Get portfolio data."""
    return jsonify(api.get_portfolio())


@app.route('/api/strategies')
def get_strategies():
    """Get strategies data."""
    return jsonify(api.get_strategies())


@app.route('/api/risk')
def get_risk():
    """Get risk management data."""
    return jsonify(api.get_risk_status())


@app.route('/api/initialize', methods=['POST'])
def initialize_engine():
    """Initialize the trading engine."""
    data = request.get_json() or {}
    symbols = data.get('symbols', ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'SPY'])
    
    result = api.initialize_engine(symbols)
    return jsonify(result)


@app.route('/api/start', methods=['POST'])
def start_trading():
    """Start trading."""
    result = api.start_trading()
    return jsonify(result)


@app.route('/api/stop', methods=['POST'])
def stop_trading():
    """Stop trading."""
    result = api.stop_trading()
    return jsonify(result)


@app.route('/api/strategy/<strategy_name>/toggle', methods=['POST'])
def toggle_strategy(strategy_name):
    """Toggle strategy active/inactive."""
    try:
        if not api.orchestrator or not api.orchestrator.strategy_engine:
            return jsonify({'success': False, 'message': 'Engine not initialized'})
        
        engine = api.orchestrator.strategy_engine
        strategy = engine.get_strategy(strategy_name)
        
        if not strategy:
            return jsonify({'success': False, 'message': 'Strategy not found'})
        
        if strategy.is_active:
            engine.deactivate_strategy(strategy_name)
            message = f'Strategy {strategy_name} deactivated'
        else:
            engine.activate_strategy(strategy_name)
            message = f'Strategy {strategy_name} activated'
        
        return jsonify({'success': True, 'message': message, 'is_active': strategy.is_active})
    
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})


@app.route('/api/emergency-stop', methods=['POST'])
def emergency_stop():
    """Trigger emergency stop."""
    try:
        if not api.orchestrator or not api.orchestrator.risk_manager:
            return jsonify({'success': False, 'message': 'Engine not initialized'})
        
        api.orchestrator.risk_manager.trigger_emergency_stop("Manual emergency stop via web interface")
        
        return jsonify({'success': True, 'message': 'Emergency stop activated'})
    
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})


@app.route('/api/clear-emergency', methods=['POST'])
def clear_emergency():
    """Clear emergency stop."""
    try:
        if not api.orchestrator or not api.orchestrator.risk_manager:
            return jsonify({'success': False, 'message': 'Engine not initialized'})
        
        api.orchestrator.risk_manager.clear_emergency_stop("Manual clear via web interface")
        
        return jsonify({'success': True, 'message': 'Emergency stop cleared'})
    
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})


@socketio.on('connect')
def handle_connect():
    """Handle client connection."""
    print('Client connected')
    emit('status', {'message': 'Connected to trading engine'})


@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection."""
    print('Client disconnected')


def emit_real_time_data():
    """Emit real-time data to connected clients."""
    try:
        status = api.get_status()
        portfolio = api.get_portfolio()
        risk_status = api.get_risk_status()
        
        socketio.emit('real_time_update', {
            'timestamp': datetime.now().isoformat(),
            'status': status,
            'portfolio': portfolio,
            'risk': risk_status
        })
    except Exception as e:
        print(f"Error emitting real-time data: {e}")


# Background thread for real-time updates
def background_thread():
    """Background thread for sending real-time updates."""
    import time
    while True:
        time.sleep(2)  # Update every 2 seconds
        emit_real_time_data()


# Start background thread
thread = threading.Thread(target=background_thread)
thread.daemon = True
thread.start()


if __name__ == '__main__':
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
