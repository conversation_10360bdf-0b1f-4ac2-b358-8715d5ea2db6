"""Historical data feed implementation for backtesting."""

import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Iterator, Union
import pandas as pd
import numpy as np

from .base_feed import BaseFeed, MarketData, OHLCV
from ..utils import get_logger, config


class HistoricalFeed(BaseFeed):
    """Historical data feed for backtesting."""
    
    def __init__(
        self,
        symbols: List[str],
        data_source: Union[str, pd.DataFrame],
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ):
        """Initialize historical feed.
        
        Args:
            symbols: List of symbols to track
            data_source: Path to CSV file or DataFrame with historical data
            start_date: Start date for backtesting
            end_date: End date for backtesting
        """
        super().__init__(symbols)
        self.logger = get_logger("trading_engine.data_feed.historical")
        self.data_source = data_source
        self.start_date = start_date or datetime(2023, 1, 1)
        self.end_date = end_date or datetime.now()
        
        self._data: Optional[pd.DataFrame] = None
        self._current_index = 0
        self._timestamps: List[datetime] = []
        self._connected = False
    
    def connect(self) -> bool:
        """Connect to historical data source."""
        try:
            self.logger.info("Loading historical data...")
            
            if isinstance(self.data_source, str):
                # Load from file
                if self.data_source.endswith('.csv'):
                    self._data = pd.read_csv(self.data_source)
                elif self.data_source.endswith('.parquet'):
                    self._data = pd.read_parquet(self.data_source)
                else:
                    raise ValueError(f"Unsupported file format: {self.data_source}")
            elif isinstance(self.data_source, pd.DataFrame):
                # Use provided DataFrame
                self._data = self.data_source.copy()
            else:
                raise ValueError("data_source must be a file path or DataFrame")
            
            # Validate and prepare data
            self._prepare_data()
            
            self._connected = True
            self.logger.info(f"Loaded historical data: {len(self._data)} rows, {len(self._timestamps)} timestamps")
            return True
            
        except Exception as e:
            self.logger.error(f"Error loading historical data: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from data source."""
        self._connected = False
        self._data = None
        self._timestamps = []
        self._current_index = 0
        self.logger.info("Disconnected from historical data feed")
    
    def is_connected(self) -> bool:
        """Check if connected to data source."""
        return self._connected and self._data is not None
    
    def _prepare_data(self):
        """Prepare and validate historical data."""
        if self._data is None:
            raise ValueError("No data loaded")
        
        # Ensure required columns exist
        required_columns = ['timestamp', 'symbol', 'open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in self._data.columns]
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")
        
        # Convert timestamp column
        if not pd.api.types.is_datetime64_any_dtype(self._data['timestamp']):
            self._data['timestamp'] = pd.to_datetime(self._data['timestamp'])
        
        # Filter by symbols
        if self.symbols:
            self._data = self._data[self._data['symbol'].isin(self.symbols)]
        
        # Filter by date range
        self._data = self._data[
            (self._data['timestamp'] >= self.start_date) &
            (self._data['timestamp'] <= self.end_date)
        ]
        
        # Sort by timestamp
        self._data = self._data.sort_values('timestamp').reset_index(drop=True)
        
        # Get unique timestamps
        self._timestamps = sorted(self._data['timestamp'].unique())
        
        if len(self._timestamps) == 0:
            raise ValueError("No data available for the specified date range and symbols")
    
    def get_latest_data(self, symbol: str) -> Optional[OHLCV]:
        """Get latest data for a symbol."""
        if not self.is_connected() or self._current_index >= len(self._timestamps):
            return None
        
        current_timestamp = self._timestamps[self._current_index]
        symbol_data = self._data[
            (self._data['timestamp'] == current_timestamp) &
            (self._data['symbol'] == symbol)
        ]
        
        if symbol_data.empty:
            return None
        
        row = symbol_data.iloc[0]
        return OHLCV(
            timestamp=row['timestamp'],
            symbol=row['symbol'],
            open=float(row['open']),
            high=float(row['high']),
            low=float(row['low']),
            close=float(row['close']),
            volume=int(row['volume'])
        )
    
    def get_historical_data(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        interval: str = '1d'
    ) -> pd.DataFrame:
        """Get historical data for a symbol."""
        if not self.is_connected():
            return pd.DataFrame()
        
        # Filter data for the symbol and date range
        filtered_data = self._data[
            (self._data['symbol'] == symbol) &
            (self._data['timestamp'] >= start_date) &
            (self._data['timestamp'] <= end_date)
        ].copy()
        
        if filtered_data.empty:
            return pd.DataFrame()
        
        # Set timestamp as index
        filtered_data.set_index('timestamp', inplace=True)
        
        # Resample if needed (basic implementation)
        if interval != '1d':
            # This is a simplified resampling - in production, you'd want more sophisticated logic
            resample_map = {
                '1m': '1T',
                '5m': '5T',
                '15m': '15T',
                '1h': '1H',
                '1d': '1D',
                '1w': '1W'
            }
            
            if interval in resample_map:
                agg_dict = {
                    'open': 'first',
                    'high': 'max',
                    'low': 'min',
                    'close': 'last',
                    'volume': 'sum'
                }
                filtered_data = filtered_data.resample(resample_map[interval]).agg(agg_dict)
                filtered_data.dropna(inplace=True)
        
        return filtered_data
    
    def stream(self) -> Iterator[MarketData]:
        """Stream historical market data."""
        if not self.is_connected():
            return
        
        self.logger.info(f"Starting historical data stream: {len(self._timestamps)} timestamps")
        
        self._current_index = 0
        
        while self._current_index < len(self._timestamps) and self.is_running:
            try:
                current_timestamp = self._timestamps[self._current_index]
                
                # Get data for all symbols at current timestamp
                timestamp_data = self._data[self._data['timestamp'] == current_timestamp]
                
                data = {}
                for _, row in timestamp_data.iterrows():
                    symbol = row['symbol']
                    ohlcv = OHLCV(
                        timestamp=row['timestamp'],
                        symbol=symbol,
                        open=float(row['open']),
                        high=float(row['high']),
                        low=float(row['low']),
                        close=float(row['close']),
                        volume=int(row['volume'])
                    )
                    data[symbol] = ohlcv
                
                if data:
                    market_data = MarketData(timestamp=current_timestamp, data=data)
                    self.current_data = market_data
                    yield market_data
                
                self._current_index += 1
                
            except Exception as e:
                self.logger.error(f"Error in historical data stream: {e}")
                break
        
        self.logger.info("Historical data stream completed")
    
    def reset(self):
        """Reset the feed to the beginning."""
        self._current_index = 0
    
    def seek_to_date(self, target_date: datetime) -> bool:
        """Seek to a specific date.
        
        Args:
            target_date: Date to seek to
            
        Returns:
            True if successful, False if date not found
        """
        try:
            # Find the closest timestamp
            for i, timestamp in enumerate(self._timestamps):
                if timestamp >= target_date:
                    self._current_index = i
                    return True
            
            # If target_date is after all timestamps, set to end
            self._current_index = len(self._timestamps)
            return False
            
        except Exception as e:
            self.logger.error(f"Error seeking to date {target_date}: {e}")
            return False
    
    def get_progress(self) -> float:
        """Get progress through the data as a percentage."""
        if not self._timestamps:
            return 0.0
        return (self._current_index / len(self._timestamps)) * 100
    
    def get_current_timestamp(self) -> Optional[datetime]:
        """Get current timestamp in the stream."""
        if 0 <= self._current_index < len(self._timestamps):
            return self._timestamps[self._current_index]
        return None
    
    def get_remaining_timestamps(self) -> int:
        """Get number of remaining timestamps."""
        return max(0, len(self._timestamps) - self._current_index)
