"""Helper functions for the trading engine."""

import re
import math
from typing import Optional, Tu<PERSON>, Dict, Any
from datetime import datetime, date


def calculate_position_size(
    account_value: float,
    risk_per_trade: float,
    entry_price: float,
    stop_loss_price: float,
    max_position_value: Optional[float] = None
) -> int:
    """Calculate position size based on risk management rules.
    
    Args:
        account_value: Total account value
        risk_per_trade: Risk per trade as a percentage (e.g., 0.02 for 2%)
        entry_price: Entry price per share
        stop_loss_price: Stop loss price per share
        max_position_value: Maximum position value (optional)
    
    Returns:
        Number of shares to trade
    """
    if entry_price <= 0 or stop_loss_price <= 0:
        return 0
    
    risk_amount = account_value * risk_per_trade
    price_diff = abs(entry_price - stop_loss_price)
    
    if price_diff == 0:
        return 0
    
    shares = int(risk_amount / price_diff)
    
    # Apply maximum position value constraint
    if max_position_value:
        max_shares = int(max_position_value / entry_price)
        shares = min(shares, max_shares)
    
    return max(0, shares)


def format_currency(amount: float, currency: str = "USD") -> str:
    """Format currency amount for display.
    
    Args:
        amount: Amount to format
        currency: Currency code
    
    Returns:
        Formatted currency string
    """
    if currency == "USD":
        return f"${amount:,.2f}"
    else:
        return f"{amount:,.2f} {currency}"


def parse_option_symbol(symbol: str) -> Optional[Dict[str, Any]]:
    """Parse option symbol into components.
    
    Args:
        symbol: Option symbol (e.g., "AAPL240119C00150000")
    
    Returns:
        Dictionary with parsed components or None if invalid
    """
    # Pattern for option symbols: TICKER + YYMMDD + C/P + STRIKE (8 digits)
    pattern = r'^([A-Z]+)(\d{6})([CP])(\d{8})$'
    match = re.match(pattern, symbol.upper())
    
    if not match:
        return None
    
    ticker, date_str, option_type, strike_str = match.groups()
    
    try:
        # Parse date (YYMMDD)
        year = 2000 + int(date_str[:2])
        month = int(date_str[2:4])
        day = int(date_str[4:6])
        expiry_date = date(year, month, day)
        
        # Parse strike (8 digits, last 3 are decimals)
        strike = float(strike_str) / 1000
        
        return {
            'ticker': ticker,
            'expiry_date': expiry_date,
            'option_type': 'call' if option_type == 'C' else 'put',
            'strike': strike
        }
    except (ValueError, TypeError):
        return None


def validate_symbol(symbol: str) -> bool:
    """Validate if symbol is properly formatted.
    
    Args:
        symbol: Stock or option symbol
    
    Returns:
        True if valid, False otherwise
    """
    if not symbol or not isinstance(symbol, str):
        return False
    
    symbol = symbol.upper().strip()
    
    # Check for stock symbol (1-5 letters)
    if re.match(r'^[A-Z]{1,5}$', symbol):
        return True
    
    # Check for option symbol
    if parse_option_symbol(symbol) is not None:
        return True
    
    return False


def round_to_tick_size(price: float, tick_size: float = 0.01) -> float:
    """Round price to the nearest tick size.
    
    Args:
        price: Price to round
        tick_size: Minimum price increment
    
    Returns:
        Rounded price
    """
    if tick_size <= 0:
        return price
    
    return round(price / tick_size) * tick_size


def calculate_days_to_expiry(expiry_date: date) -> int:
    """Calculate days to expiry from today.
    
    Args:
        expiry_date: Option expiry date
    
    Returns:
        Number of days to expiry
    """
    today = date.today()
    return (expiry_date - today).days


def calculate_annualized_return(
    start_value: float,
    end_value: float,
    days: int
) -> float:
    """Calculate annualized return.
    
    Args:
        start_value: Starting portfolio value
        end_value: Ending portfolio value
        days: Number of days in the period
    
    Returns:
        Annualized return as a percentage
    """
    if start_value <= 0 or days <= 0:
        return 0.0
    
    total_return = (end_value / start_value) - 1
    years = days / 365.25
    
    if years == 0:
        return 0.0
    
    annualized_return = (1 + total_return) ** (1 / years) - 1
    return annualized_return * 100


def calculate_sharpe_ratio(
    returns: list,
    risk_free_rate: float = 0.02
) -> float:
    """Calculate Sharpe ratio from a list of returns.
    
    Args:
        returns: List of periodic returns
        risk_free_rate: Annual risk-free rate
    
    Returns:
        Sharpe ratio
    """
    if not returns or len(returns) < 2:
        return 0.0
    
    import numpy as np
    
    returns_array = np.array(returns)
    excess_returns = returns_array - (risk_free_rate / 252)  # Daily risk-free rate
    
    if np.std(excess_returns) == 0:
        return 0.0
    
    return np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252)
