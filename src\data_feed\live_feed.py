"""Live data feed implementation using robin_stocks."""

import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Iterator
import pandas as pd
import robin_stocks.robinhood as rh

from .base_feed import BaseFeed, MarketData, OHLCV
from ..utils import get_logger, config


class LiveFeed(BaseFeed):
    """Live data feed using Robinhood API."""
    
    def __init__(self, symbols: List[str], update_interval: int = 60):
        """Initialize live feed.
        
        Args:
            symbols: List of symbols to track
            update_interval: Update interval in seconds
        """
        super().__init__(symbols)
        self.logger = get_logger("trading_engine.data_feed.live")
        self.update_interval = update_interval
        self._connected = False
        self._update_thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()
        self._data_cache: Dict[str, OHLCV] = {}
        self._last_update: Optional[datetime] = None
    
    def connect(self) -> bool:
        """Connect to Robinhood data source."""
        try:
            # Check if we need to login (this should be handled by the broker)
            # For data feed, we'll assume login is already done
            self._connected = True
            self.logger.info("Connected to live data feed")
            return True
        except Exception as e:
            self.logger.error(f"Error connecting to live data feed: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from data source."""
        self._connected = False
        self._stop_event.set()
        
        if self._update_thread and self._update_thread.is_alive():
            self._update_thread.join(timeout=5)
        
        self.logger.info("Disconnected from live data feed")
    
    def is_connected(self) -> bool:
        """Check if connected to data source."""
        return self._connected
    
    def get_latest_data(self, symbol: str) -> Optional[OHLCV]:
        """Get latest data for a symbol."""
        if not self.is_connected():
            return None
        
        try:
            # Get latest price
            price_data = rh.stocks.get_latest_price(symbol, includeExtendedHours=True)
            if not price_data or len(price_data) == 0:
                return None
            
            current_price = float(price_data[0])
            
            # Get historical data for OHLC (use 1-day interval)
            historical = rh.stocks.get_stock_historicals(
                symbol,
                interval='day',
                span='week'
            )
            
            if historical and len(historical) > 0:
                latest = historical[-1]
                
                return OHLCV(
                    timestamp=datetime.now(),
                    symbol=symbol,
                    open=float(latest['open_price']),
                    high=float(latest['high_price']),
                    low=float(latest['low_price']),
                    close=current_price,
                    volume=int(latest['volume'])
                )
            else:
                # Fallback: use current price for all OHLC values
                return OHLCV(
                    timestamp=datetime.now(),
                    symbol=symbol,
                    open=current_price,
                    high=current_price,
                    low=current_price,
                    close=current_price,
                    volume=0
                )
                
        except Exception as e:
            self.logger.error(f"Error getting latest data for {symbol}: {e}")
            return None
    
    def get_historical_data(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        interval: str = '1d'
    ) -> pd.DataFrame:
        """Get historical data for a symbol."""
        if not self.is_connected():
            return pd.DataFrame()
        
        try:
            # Map interval to Robinhood format
            interval_map = {
                '1m': ('5minute', 'day'),
                '5m': ('5minute', 'week'),
                '15m': ('15minute', 'week'),
                '1h': ('hour', 'month'),
                '1d': ('day', 'year'),
                '1w': ('week', '5year')
            }
            
            rh_interval, rh_span = interval_map.get(interval, ('day', 'year'))
            
            # Get historical data from Robinhood
            historical = rh.stocks.get_stock_historicals(
                symbol,
                interval=rh_interval,
                span=rh_span
            )
            
            if not historical:
                return pd.DataFrame()
            
            # Convert to DataFrame
            data = []
            for bar in historical:
                timestamp = datetime.fromisoformat(bar['begins_at'].replace('Z', '+00:00'))
                
                # Filter by date range
                if start_date <= timestamp <= end_date:
                    data.append({
                        'timestamp': timestamp,
                        'symbol': symbol,
                        'open': float(bar['open_price']),
                        'high': float(bar['high_price']),
                        'low': float(bar['low_price']),
                        'close': float(bar['close_price']),
                        'volume': int(bar['volume'])
                    })
            
            df = pd.DataFrame(data)
            if not df.empty:
                df.set_index('timestamp', inplace=True)
            
            return df
            
        except Exception as e:
            self.logger.error(f"Error getting historical data for {symbol}: {e}")
            return pd.DataFrame()
    
    def stream(self) -> Iterator[MarketData]:
        """Stream market data."""
        if not self.is_connected():
            return
        
        self.logger.info(f"Starting live data stream for {len(self.symbols)} symbols")
        
        while self.is_running and self.is_connected():
            try:
                # Update data for all symbols
                market_data = self._fetch_market_data()
                
                if market_data:
                    self.current_data = market_data
                    yield market_data
                
                # Wait for next update
                time.sleep(self.update_interval)
                
            except Exception as e:
                self.logger.error(f"Error in data stream: {e}")
                time.sleep(5)  # Wait before retrying
    
    def _fetch_market_data(self) -> Optional[MarketData]:
        """Fetch current market data for all symbols."""
        try:
            timestamp = datetime.now()
            data = {}
            
            for symbol in self.symbols:
                ohlcv = self.get_latest_data(symbol)
                if ohlcv:
                    data[symbol] = ohlcv
                    self._data_cache[symbol] = ohlcv
            
            if data:
                self._last_update = timestamp
                return MarketData(timestamp=timestamp, data=data)
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error fetching market data: {e}")
            return None
    
    def start_background_updates(self):
        """Start background thread for data updates."""
        if self._update_thread and self._update_thread.is_alive():
            return
        
        self._stop_event.clear()
        self._update_thread = threading.Thread(target=self._background_update_loop)
        self._update_thread.daemon = True
        self._update_thread.start()
        
        self.logger.info("Started background data updates")
    
    def _background_update_loop(self):
        """Background loop for updating data."""
        while not self._stop_event.is_set() and self.is_connected():
            try:
                self._fetch_market_data()
                self._stop_event.wait(self.update_interval)
            except Exception as e:
                self.logger.error(f"Error in background update loop: {e}")
                self._stop_event.wait(5)
    
    def get_cached_data(self, symbol: str) -> Optional[OHLCV]:
        """Get cached data for a symbol."""
        return self._data_cache.get(symbol)
    
    def get_last_update_time(self) -> Optional[datetime]:
        """Get timestamp of last data update."""
        return self._last_update
