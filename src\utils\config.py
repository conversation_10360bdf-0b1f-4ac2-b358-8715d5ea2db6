"""Configuration management for the trading engine."""

import os
import yaml
import logging.config
from pathlib import Path
from typing import Dict, Any, Optional
from dotenv import load_dotenv


class Config:
    """Central configuration manager for the trading engine."""
    
    def __init__(self, env_file: Optional[str] = None):
        """Initialize configuration.
        
        Args:
            env_file: Path to .env file. If None, looks for .env in config/
        """
        self.project_root = Path(__file__).parent.parent.parent
        self.config_dir = self.project_root / "config"
        self.data_dir = self.project_root / "data"
        self.logs_dir = self.project_root / "logs"
        
        # Ensure directories exist
        self.logs_dir.mkdir(exist_ok=True)
        self.data_dir.mkdir(exist_ok=True)
        
        # Load environment variables
        if env_file is None:
            env_file = self.config_dir / ".env"
        
        if Path(env_file).exists():
            load_dotenv(env_file)
        
        # Setup logging
        self._setup_logging()
        
        # Load configuration
        self._load_config()
    
    def _setup_logging(self):
        """Setup logging configuration."""
        logging_config_path = self.config_dir / "logging.yaml"
        
        if logging_config_path.exists():
            with open(logging_config_path, 'r') as f:
                logging_config = yaml.safe_load(f)
            logging.config.dictConfig(logging_config)
        else:
            # Fallback to basic logging
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
            )
    
    def _load_config(self):
        """Load configuration from environment variables."""
        # Trading mode
        self.mode = os.getenv("MODE", "paper").lower()
        
        # Robinhood credentials
        self.rh_username = os.getenv("RH_USERNAME")
        self.rh_password = os.getenv("RH_PASSWORD")
        self.rh_mfa_secret = os.getenv("RH_MFA_SECRET")
        
        # Risk management
        self.max_daily_drawdown = float(os.getenv("MAX_DAILY_DRAWDOWN", "0.03"))
        self.max_position_size = float(os.getenv("MAX_POSITION_SIZE", "0.02"))
        self.max_concurrent_notional = float(os.getenv("MAX_CONCURRENT_NOTIONAL", "0.10"))
        
        # Portfolio settings
        self.initial_capital = float(os.getenv("INITIAL_CAPITAL", "10000.0"))
        self.commission_per_trade = float(os.getenv("COMMISSION_PER_TRADE", "0.0"))
        
        # Strategy parameters
        self.ema_short_period = int(os.getenv("EMA_SHORT_PERIOD", "12"))
        self.ema_long_period = int(os.getenv("EMA_LONG_PERIOD", "26"))
        self.atr_period = int(os.getenv("ATR_PERIOD", "14"))
        self.ttm_squeeze_length = int(os.getenv("TTM_SQUEEZE_LENGTH", "20"))
        self.ttm_squeeze_mult = float(os.getenv("TTM_SQUEEZE_MULT", "2.0"))
        
        # Options strategy
        self.min_ev_threshold = float(os.getenv("MIN_EV_THRESHOLD", "0.0"))
        self.min_pop_threshold = float(os.getenv("MIN_POP_THRESHOLD", "0.6"))
        self.max_dte = int(os.getenv("MAX_DTE", "45"))
        self.min_dte = int(os.getenv("MIN_DTE", "7"))
        
        # Data settings
        self.data_provider = os.getenv("DATA_PROVIDER", "robinhood")
        self.historical_data_path = Path(os.getenv("HISTORICAL_DATA_PATH", self.data_dir / "historical"))
        self.backtest_start_date = os.getenv("BACKTEST_START_DATE", "2023-01-01")
        self.backtest_end_date = os.getenv("BACKTEST_END_DATE", "2024-01-01")
        
        # Paper trading settings
        self.paper_slippage = float(os.getenv("PAPER_SLIPPAGE", "0.001"))
        self.paper_latency_ms = int(os.getenv("PAPER_LATENCY_MS", "100"))
        
        # Live trading safety
        self.live_trading_enabled = os.getenv("LIVE_TRADING_ENABLED", "false").lower() == "true"
        self.max_order_value = float(os.getenv("MAX_ORDER_VALUE", "1000.0"))
        
        # Monitoring
        self.slack_webhook_url = os.getenv("SLACK_WEBHOOK_URL")
        self.telegram_bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
        self.telegram_chat_id = os.getenv("TELEGRAM_CHAT_ID")
    
    def is_live_mode(self) -> bool:
        """Check if running in live trading mode."""
        return self.mode == "live" and self.live_trading_enabled
    
    def is_paper_mode(self) -> bool:
        """Check if running in paper trading mode."""
        return self.mode == "paper"
    
    def validate_credentials(self) -> bool:
        """Validate that required credentials are present."""
        if self.is_live_mode():
            return all([
                self.rh_username,
                self.rh_password,
                self.rh_mfa_secret
            ])
        return True  # Paper mode doesn't require credentials
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            attr: getattr(self, attr)
            for attr in dir(self)
            if not attr.startswith('_') and not callable(getattr(self, attr))
        }


# Global configuration instance
config = Config()
