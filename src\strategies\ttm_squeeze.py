"""TTM Squeeze strategy implementation."""

from datetime import datetime
from typing import Dict, List, Optional, Any
import pandas as pd
import numpy as np

from .base_strategy import BaseStrategy, Signal, SignalType, StrategyResult
from .technical_indicators import TechnicalIndicators
from ..data_feed import MarketData, OHLCV
from ..broker import OrderType
from ..utils import get_logger, config


class TTMSqueezeStrategy(BaseStrategy):
    """TTM Squeeze strategy for identifying volatility breakouts."""
    
    def __init__(self, symbols: List[str], parameters: Optional[Dict[str, Any]] = None):
        """Initialize TTM Squeeze strategy.
        
        Args:
            symbols: List of symbols to trade
            parameters: Strategy parameters
        """
        default_params = {
            'length': config.ttm_squeeze_length,  # 20
            'mult': config.ttm_squeeze_mult,      # 2.0
            'min_squeeze_periods': 5,             # Minimum periods in squeeze
            'momentum_threshold': 0.0,            # Momentum threshold for signals
            'confidence_threshold': 0.6,          # Minimum confidence for signals
            'atr_multiplier': 1.5,               # ATR multiplier for stop loss
            'profit_target_ratio': 2.0,          # Profit target as ratio of stop loss
            'max_history': 200                   # Maximum periods to keep in history
        }
        
        if parameters:
            default_params.update(parameters)
        
        super().__init__("TTM_Squeeze", symbols, default_params)
        self.logger = get_logger("trading_engine.strategy.ttm_squeeze")
        
        # Strategy state
        self.squeeze_state: Dict[str, bool] = {}
        self.squeeze_count: Dict[str, int] = {}
        self.last_momentum: Dict[str, float] = {}
        
        # Initialize state for each symbol
        for symbol in symbols:
            self.squeeze_state[symbol] = False
            self.squeeze_count[symbol] = 0
            self.last_momentum[symbol] = 0.0
    
    def generate_signals(self, market_data: MarketData) -> StrategyResult:
        """Generate TTM Squeeze signals."""
        if not self.is_active:
            return StrategyResult(self.name, market_data.timestamp, [])
        
        signals = []
        indicators = {}
        
        for symbol in self.symbols:
            if symbol not in market_data.data:
                continue
            
            try:
                signal = self._analyze_symbol(symbol, market_data.timestamp)
                if signal:
                    signals.append(signal)
                    self.record_signal(signal)
                
                # Get indicators for this symbol
                symbol_indicators = self._get_indicators(symbol)
                if symbol_indicators:
                    indicators[symbol] = symbol_indicators
                    
            except Exception as e:
                self.logger.error(f"Error analyzing {symbol}: {e}")
                continue
        
        self.last_update = market_data.timestamp
        
        return StrategyResult(
            strategy_name=self.name,
            timestamp=market_data.timestamp,
            signals=signals,
            indicators=indicators
        )
    
    def update_data(self, market_data: MarketData):
        """Update internal data with new market data."""
        for symbol, ohlcv in market_data.data.items():
            if symbol in self.symbols:
                self._update_symbol_data(symbol, ohlcv)
    
    def _analyze_symbol(self, symbol: str, timestamp: datetime) -> Optional[Signal]:
        """Analyze a symbol for TTM Squeeze signals."""
        data = self.get_data_for_symbol(symbol)
        
        if len(data) < self.get_parameter('length') + 10:
            return None
        
        # Calculate TTM Squeeze indicators
        high = data['high']
        low = data['low']
        close = data['close']
        
        squeeze_on, momentum = TechnicalIndicators.ttm_squeeze(
            high, low, close,
            length=self.get_parameter('length'),
            mult=self.get_parameter('mult')
        )
        
        if len(squeeze_on) == 0 or len(momentum) == 0:
            return None
        
        # Get current values
        current_squeeze = squeeze_on.iloc[-1] if not pd.isna(squeeze_on.iloc[-1]) else False
        current_momentum = momentum.iloc[-1] if not pd.isna(momentum.iloc[-1]) else 0.0
        previous_momentum = momentum.iloc[-2] if len(momentum) > 1 and not pd.isna(momentum.iloc[-2]) else 0.0
        
        # Update squeeze state
        prev_squeeze_state = self.squeeze_state.get(symbol, False)
        self.squeeze_state[symbol] = current_squeeze
        
        # Count squeeze periods
        if current_squeeze:
            self.squeeze_count[symbol] = self.squeeze_count.get(symbol, 0) + 1
        else:
            self.squeeze_count[symbol] = 0
        
        # Check for squeeze release (breakout)
        if prev_squeeze_state and not current_squeeze:
            # Squeeze has been released - potential breakout
            min_squeeze_periods = self.get_parameter('min_squeeze_periods')
            
            if self.squeeze_count[symbol] >= min_squeeze_periods:
                return self._generate_breakout_signal(
                    symbol, timestamp, current_momentum, previous_momentum, close.iloc[-1]
                )
        
        # Check for momentum change during squeeze
        elif current_squeeze:
            momentum_threshold = self.get_parameter('momentum_threshold')
            
            # Look for momentum direction change
            if (previous_momentum <= momentum_threshold < current_momentum or
                previous_momentum >= momentum_threshold > current_momentum):
                
                return self._generate_momentum_signal(
                    symbol, timestamp, current_momentum, close.iloc[-1]
                )
        
        # Update last momentum
        self.last_momentum[symbol] = current_momentum
        
        return None
    
    def _generate_breakout_signal(
        self,
        symbol: str,
        timestamp: datetime,
        current_momentum: float,
        previous_momentum: float,
        current_price: float
    ) -> Optional[Signal]:
        """Generate breakout signal after squeeze release."""
        
        # Determine direction based on momentum
        if current_momentum > 0:
            signal_type = SignalType.BUY
        elif current_momentum < 0:
            signal_type = SignalType.SELL
        else:
            return None  # No clear direction
        
        # Calculate confidence based on momentum strength and squeeze duration
        momentum_strength = abs(current_momentum)
        squeeze_duration = self.squeeze_count[symbol]
        
        # Higher confidence for stronger momentum and longer squeeze
        confidence = min(0.9, 0.5 + (momentum_strength * 0.1) + (squeeze_duration * 0.02))
        
        # Only generate signal if confidence is above threshold
        if confidence < self.get_parameter('confidence_threshold'):
            return None
        
        # Calculate stop loss and take profit
        data = self.get_data_for_symbol(symbol)
        atr_value = TechnicalIndicators.atr(
            data['high'], data['low'], data['close'],
            period=self.get_parameter('length')
        ).iloc[-1]
        
        atr_multiplier = self.get_parameter('atr_multiplier')
        profit_ratio = self.get_parameter('profit_target_ratio')
        
        if signal_type == SignalType.BUY:
            stop_loss = current_price - (atr_value * atr_multiplier)
            take_profit = current_price + (atr_value * atr_multiplier * profit_ratio)
        else:
            stop_loss = current_price + (atr_value * atr_multiplier)
            take_profit = current_price - (atr_value * atr_multiplier * profit_ratio)
        
        return Signal(
            symbol=symbol,
            signal_type=signal_type,
            timestamp=timestamp,
            price=current_price,
            confidence=confidence,
            order_type=OrderType.MARKET,
            stop_loss=stop_loss,
            take_profit=take_profit,
            metadata={
                'strategy': 'ttm_squeeze_breakout',
                'momentum': current_momentum,
                'squeeze_duration': squeeze_duration,
                'atr': atr_value
            }
        )
    
    def _generate_momentum_signal(
        self,
        symbol: str,
        timestamp: datetime,
        current_momentum: float,
        current_price: float
    ) -> Optional[Signal]:
        """Generate momentum signal during squeeze."""
        
        momentum_threshold = self.get_parameter('momentum_threshold')
        
        if current_momentum > momentum_threshold:
            signal_type = SignalType.BUY
        elif current_momentum < momentum_threshold:
            signal_type = SignalType.SELL
        else:
            return None
        
        # Lower confidence for momentum signals during squeeze
        confidence = min(0.7, 0.4 + abs(current_momentum) * 0.1)
        
        if confidence < self.get_parameter('confidence_threshold'):
            return None
        
        # Calculate stop loss based on recent volatility
        data = self.get_data_for_symbol(symbol)
        atr_value = TechnicalIndicators.atr(
            data['high'], data['low'], data['close'],
            period=self.get_parameter('length')
        ).iloc[-1]
        
        atr_multiplier = self.get_parameter('atr_multiplier') * 0.8  # Tighter stops during squeeze
        
        if signal_type == SignalType.BUY:
            stop_loss = current_price - (atr_value * atr_multiplier)
            take_profit = current_price + (atr_value * atr_multiplier * 1.5)
        else:
            stop_loss = current_price + (atr_value * atr_multiplier)
            take_profit = current_price - (atr_value * atr_multiplier * 1.5)
        
        return Signal(
            symbol=symbol,
            signal_type=signal_type,
            timestamp=timestamp,
            price=current_price,
            confidence=confidence,
            order_type=OrderType.MARKET,
            stop_loss=stop_loss,
            take_profit=take_profit,
            metadata={
                'strategy': 'ttm_squeeze_momentum',
                'momentum': current_momentum,
                'squeeze_active': True,
                'atr': atr_value
            }
        )
    
    def _get_indicators(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get current indicators for a symbol."""
        data = self.get_data_for_symbol(symbol)
        
        if len(data) < self.get_parameter('length'):
            return None
        
        try:
            high = data['high']
            low = data['low']
            close = data['close']
            
            squeeze_on, momentum = TechnicalIndicators.ttm_squeeze(
                high, low, close,
                length=self.get_parameter('length'),
                mult=self.get_parameter('mult')
            )
            
            # Bollinger Bands
            bb_upper, bb_middle, bb_lower = TechnicalIndicators.bollinger_bands(
                close, period=self.get_parameter('length'), std_dev=self.get_parameter('mult')
            )
            
            # ATR
            atr_value = TechnicalIndicators.atr(high, low, close, period=self.get_parameter('length'))
            
            return {
                'squeeze_on': squeeze_on.iloc[-1] if len(squeeze_on) > 0 else False,
                'momentum': momentum.iloc[-1] if len(momentum) > 0 else 0.0,
                'squeeze_count': self.squeeze_count.get(symbol, 0),
                'bb_upper': bb_upper.iloc[-1] if len(bb_upper) > 0 else None,
                'bb_middle': bb_middle.iloc[-1] if len(bb_middle) > 0 else None,
                'bb_lower': bb_lower.iloc[-1] if len(bb_lower) > 0 else None,
                'atr': atr_value.iloc[-1] if len(atr_value) > 0 else None,
                'current_price': close.iloc[-1] if len(close) > 0 else None
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating indicators for {symbol}: {e}")
            return None
