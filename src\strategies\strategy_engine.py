"""Strategy engine for coordinating multiple trading strategies."""

from datetime import datetime
from typing import Dict, List, Optional, Any, Type
import threading
import time

from .base_strategy import BaseStrategy, Signal, StrategyResult
from .ttm_squeeze import TTMSqueezeStrategy
from .ema_crossover import EMACrossoverStrategy
from .atr_breakout import ATRBreakoutStrategy
from .options_analyzer import OptionsAnalyzer
from ..data_feed import MarketData
from ..utils import get_logger, config


class StrategyEngine:
    """Engine for managing and executing multiple trading strategies."""
    
    def __init__(self):
        self.logger = get_logger("trading_engine.strategy_engine")
        self.strategies: Dict[str, BaseStrategy] = {}
        self.options_analyzer = OptionsAnalyzer()
        
        # Strategy results
        self.latest_results: Dict[str, StrategyResult] = {}
        self.all_signals: List[Signal] = []
        
        # Performance tracking
        self.total_signals_generated = 0
        self.strategies_executed = 0
        self.last_execution_time: Optional[datetime] = None
        
        # Threading
        self._lock = threading.Lock()
        self.is_running = False
    
    def add_strategy(self, strategy: BaseStrategy, replace_existing: bool = False):
        """Add a strategy to the engine.
        
        Args:
            strategy: Strategy instance to add
            replace_existing: Whether to replace existing strategy with same name
        """
        with self._lock:
            if strategy.name in self.strategies and not replace_existing:
                raise ValueError(f"Strategy '{strategy.name}' already exists")
            
            self.strategies[strategy.name] = strategy
            self.logger.info(f"Added strategy: {strategy.name}")
    
    def remove_strategy(self, strategy_name: str):
        """Remove a strategy from the engine.
        
        Args:
            strategy_name: Name of strategy to remove
        """
        with self._lock:
            if strategy_name in self.strategies:
                del self.strategies[strategy_name]
                if strategy_name in self.latest_results:
                    del self.latest_results[strategy_name]
                self.logger.info(f"Removed strategy: {strategy_name}")
    
    def get_strategy(self, strategy_name: str) -> Optional[BaseStrategy]:
        """Get a strategy by name.
        
        Args:
            strategy_name: Name of strategy
            
        Returns:
            Strategy instance or None if not found
        """
        return self.strategies.get(strategy_name)
    
    def list_strategies(self) -> List[str]:
        """Get list of strategy names.
        
        Returns:
            List of strategy names
        """
        return list(self.strategies.keys())
    
    def activate_strategy(self, strategy_name: str):
        """Activate a strategy.
        
        Args:
            strategy_name: Name of strategy to activate
        """
        strategy = self.strategies.get(strategy_name)
        if strategy:
            strategy.activate()
            self.logger.info(f"Activated strategy: {strategy_name}")
    
    def deactivate_strategy(self, strategy_name: str):
        """Deactivate a strategy.
        
        Args:
            strategy_name: Name of strategy to deactivate
        """
        strategy = self.strategies.get(strategy_name)
        if strategy:
            strategy.deactivate()
            self.logger.info(f"Deactivated strategy: {strategy_name}")
    
    def execute_strategies(self, market_data: MarketData) -> List[Signal]:
        """Execute all active strategies and return combined signals.
        
        Args:
            market_data: Current market data
            
        Returns:
            List of all generated signals
        """
        with self._lock:
            all_signals = []
            
            # Update all strategies with new data
            for strategy in self.strategies.values():
                if strategy.is_active:
                    try:
                        strategy.update_data(market_data)
                    except Exception as e:
                        self.logger.error(f"Error updating strategy {strategy.name}: {e}")
            
            # Generate signals from all strategies
            for strategy_name, strategy in self.strategies.items():
                if not strategy.is_active:
                    continue
                
                try:
                    result = strategy.generate_signals(market_data)
                    self.latest_results[strategy_name] = result
                    
                    if result.has_signals():
                        all_signals.extend(result.signals)
                        self.logger.info(f"Strategy {strategy_name} generated {len(result.signals)} signals")
                    
                    self.strategies_executed += 1
                    
                except Exception as e:
                    self.logger.error(f"Error executing strategy {strategy_name}: {e}")
                    continue
            
            # Filter and rank signals
            filtered_signals = self._filter_signals(all_signals)
            
            # Update tracking
            self.total_signals_generated += len(filtered_signals)
            self.all_signals.extend(filtered_signals)
            self.last_execution_time = market_data.timestamp
            
            # Keep only recent signals in memory
            max_signals = 1000
            if len(self.all_signals) > max_signals:
                self.all_signals = self.all_signals[-max_signals:]
            
            return filtered_signals
    
    def _filter_signals(self, signals: List[Signal]) -> List[Signal]:
        """Filter and rank signals based on confidence and other criteria.
        
        Args:
            signals: List of raw signals
            
        Returns:
            List of filtered and ranked signals
        """
        if not signals:
            return []
        
        # Remove duplicate signals for the same symbol
        symbol_signals = {}
        for signal in signals:
            if signal.symbol not in symbol_signals:
                symbol_signals[signal.symbol] = []
            symbol_signals[signal.symbol].append(signal)
        
        filtered_signals = []
        
        for symbol, symbol_signal_list in symbol_signals.items():
            if len(symbol_signal_list) == 1:
                filtered_signals.append(symbol_signal_list[0])
            else:
                # Multiple signals for same symbol - choose highest confidence
                best_signal = max(symbol_signal_list, key=lambda s: s.confidence)
                
                # Check for conflicting signals
                buy_signals = [s for s in symbol_signal_list if s.signal_type.value in ['buy']]
                sell_signals = [s for s in symbol_signal_list if s.signal_type.value in ['sell']]
                
                if buy_signals and sell_signals:
                    # Conflicting signals - only use if confidence difference is significant
                    best_buy = max(buy_signals, key=lambda s: s.confidence) if buy_signals else None
                    best_sell = max(sell_signals, key=lambda s: s.confidence) if sell_signals else None
                    
                    if best_buy and best_sell:
                        confidence_diff = abs(best_buy.confidence - best_sell.confidence)
                        if confidence_diff > 0.2:  # 20% confidence difference required
                            best_signal = best_buy if best_buy.confidence > best_sell.confidence else best_sell
                        else:
                            continue  # Skip conflicting signals with similar confidence
                
                filtered_signals.append(best_signal)
        
        # Sort by confidence (descending)
        filtered_signals.sort(key=lambda s: s.confidence, reverse=True)
        
        return filtered_signals
    
    def get_latest_results(self) -> Dict[str, StrategyResult]:
        """Get latest results from all strategies.
        
        Returns:
            Dictionary of strategy name -> latest result
        """
        return self.latest_results.copy()
    
    def get_recent_signals(self, count: int = 50) -> List[Signal]:
        """Get recent signals from all strategies.
        
        Args:
            count: Number of recent signals to return
            
        Returns:
            List of recent signals
        """
        return self.all_signals[-count:] if self.all_signals else []
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics for the strategy engine.
        
        Returns:
            Dictionary with performance metrics
        """
        strategy_stats = {}
        for name, strategy in self.strategies.items():
            strategy_stats[name] = strategy.get_performance_stats()
        
        return {
            'total_strategies': len(self.strategies),
            'active_strategies': sum(1 for s in self.strategies.values() if s.is_active),
            'total_signals_generated': self.total_signals_generated,
            'strategies_executed': self.strategies_executed,
            'last_execution_time': self.last_execution_time.isoformat() if self.last_execution_time else None,
            'strategy_stats': strategy_stats
        }
    
    def reset_all_strategies(self):
        """Reset all strategies to initial state."""
        with self._lock:
            for strategy in self.strategies.values():
                strategy.reset()
            
            self.latest_results.clear()
            self.all_signals.clear()
            self.total_signals_generated = 0
            self.strategies_executed = 0
            self.last_execution_time = None
            
            self.logger.info("Reset all strategies")
    
    def create_default_strategies(self, symbols: List[str]) -> Dict[str, BaseStrategy]:
        """Create default set of strategies.
        
        Args:
            symbols: List of symbols to trade
            
        Returns:
            Dictionary of created strategies
        """
        strategies = {}
        
        # TTM Squeeze Strategy
        ttm_strategy = TTMSqueezeStrategy(symbols)
        strategies['ttm_squeeze'] = ttm_strategy
        self.add_strategy(ttm_strategy)
        
        # EMA Crossover Strategy
        ema_strategy = EMACrossoverStrategy(symbols)
        strategies['ema_crossover'] = ema_strategy
        self.add_strategy(ema_strategy)
        
        # ATR Breakout Strategy
        atr_strategy = ATRBreakoutStrategy(symbols)
        strategies['atr_breakout'] = atr_strategy
        self.add_strategy(atr_strategy)
        
        self.logger.info(f"Created default strategies for {len(symbols)} symbols")
        
        return strategies
