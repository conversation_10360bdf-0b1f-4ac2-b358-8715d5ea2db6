#!/usr/bin/env python3
"""Startup script for the trading engine web interface."""

import os
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from app import app, socketio

if __name__ == '__main__':
    # Configuration
    debug = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    host = os.getenv('FLASK_HOST', '0.0.0.0')
    port = int(os.getenv('FLASK_PORT', 5000))
    
    print(f"🚀 Starting Trading Engine Web Interface")
    print(f"📡 Server: http://{host}:{port}")
    print(f"🔧 Debug mode: {debug}")
    print(f"⚠️  Make sure the trading engine is configured before use")
    
    # Run the application
    socketio.run(
        app,
        debug=debug,
        host=host,
        port=port,
        allow_unsafe_werkzeug=True
    )
