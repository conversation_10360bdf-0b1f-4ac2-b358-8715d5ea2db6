# Robinhood Trading Engine

A comprehensive algorithmic trading engine built on top of the Robinhood platform, featuring both paper trading simulation and live trading capabilities.

## Features

- **Dual Mode Operation**: Seamless switching between paper trading (simulation) and live trading
- **Multiple Asset Classes**: Support for stocks, options, and cryptocurrency
- **Advanced Strategies**: TTM Squeeze, EMA crossovers, ATR breakouts, and options EV/POP analysis
- **Risk Management**: Position sizing, stop-losses, portfolio limits, and drawdown protection
- **Comprehensive Testing**: Unit and integration tests with backtesting capabilities
- **Monitoring & Alerts**: Real-time logging, Slack/Telegram notifications
- **CI/CD Ready**: Docker support and automated testing pipeline

## Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <your-repo-url>
cd robinhood-trading-engine

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

```bash
# Copy environment template
cp config/.env.template config/.env

# Edit configuration
nano config/.env
```

**Required Configuration:**
- Set `MODE=paper` for simulation or `MODE=live` for real trading
- Add Robinhood credentials (for live mode)
- Configure risk management parameters
- Set up monitoring webhooks (optional)

### 3. Paper Trading (Recommended First Step)

```bash
# Run in paper trading mode
python -m src.orchestrator
```

### 4. Live Trading (After Testing)

```bash
# Ensure live trading is properly configured
# Set MODE=live and LIVE_TRADING_ENABLED=true in .env
python -m src.orchestrator
```

## Project Structure

```
robinhood-trading-engine/
├── config/                 # Configuration files
│   ├── .env.template      # Environment variables template
│   └── logging.yaml       # Logging configuration
├── data/                  # Historical data and databases
├── src/                   # Source code
│   ├── broker/           # Execution layer (live & paper)
│   ├── data_feed/        # Market data ingestion
│   ├── strategies/       # Signal generation & analysis
│   ├── risk/            # Risk management system
│   ├── utils/           # Common utilities
│   └── orchestrator.py  # Main event loop
├── tests/               # Test suites
│   ├── unit/           # Unit tests
│   └── integration/    # Integration tests
├── logs/               # Log files (auto-created)
├── requirements.txt    # Python dependencies
├── Dockerfile         # Container configuration
└── README.md          # This file
```

## Key Components

### Broker Layer
- **LiveBroker**: Interfaces with Robinhood API via `robin_stocks`
- **PaperBroker**: Simulation engine with realistic execution modeling

### Strategy Engine
- **Signal Generators**: Technical analysis indicators
- **EV/POP Calculator**: Options expected value and probability analysis
- **Portfolio Optimizer**: Position sizing and allocation

### Risk Management
- **Position Sizing**: Kelly criterion and fixed-fractional methods
- **Stop Losses**: Time-based and volatility-based exits
- **Portfolio Limits**: Drawdown protection and exposure limits

### Data Pipeline
- **Live Data**: Real-time market data via Robinhood
- **Historical Data**: Backtesting with CSV/Parquet files
- **Multiple Timeframes**: Support for various chart intervals

## Trading Strategies

### 1. TTM Squeeze
Identifies periods of low volatility followed by explosive moves.

### 2. EMA Crossover
Classic trend-following strategy using exponential moving averages.

### 3. ATR Breakout
Volatility-based breakout strategy using Average True Range.

### 4. Options EV/POP
Expected value and probability of profit analysis for options trading.

## Risk Management

### Position Sizing
- Maximum 2% of NAV per position (configurable)
- Kelly criterion for optimal sizing
- Volatility-adjusted position sizes

### Stop Losses
- Time-based stops (1 day before expiry for options)
- ATR-based stops (1x ATR adverse move)
- Profit targets (+50% configurable)

### Portfolio Protection
- Maximum 3% daily drawdown limit
- Maximum 10% concurrent notional exposure
- Real-time P&L monitoring

## Testing

### Unit Tests
```bash
pytest tests/unit/ -v
```

### Integration Tests
```bash
pytest tests/integration/ -v
```

### Backtesting
```bash
python -m src.strategies.backtest --start-date 2023-01-01 --end-date 2024-01-01
```

## Monitoring

### Logging
- Structured logging with rotation
- Separate files for trades, errors, and general logs
- JSON format for easy parsing

### Alerts
- Slack webhook integration
- Telegram bot notifications
- Email alerts (configurable)

### Metrics
- Real-time P&L tracking
- Sharpe ratio calculation
- Maximum drawdown monitoring

## Safety Features

### Paper Trading
- Complete simulation environment
- No real money at risk
- Realistic execution modeling with slippage

### Live Trading Safeguards
- Double confirmation for live mode
- Maximum order value limits
- Emergency stop functionality
- Comprehensive logging

## Development

### Code Quality
```bash
# Linting
flake8 src/

# Type checking
mypy src/

# Code formatting
black src/
```

### Adding New Strategies
1. Create strategy class in `src/strategies/`
2. Implement required interface methods
3. Add configuration parameters
4. Write unit tests
5. Backtest thoroughly before live deployment

## Deployment

### Docker
```bash
# Build image
docker build -t trading-engine .

# Run container
docker run -d --name trading-engine \
  --env-file config/.env \
  -v $(pwd)/logs:/app/logs \
  trading-engine
```

### Production Considerations
- Use environment-specific configuration
- Set up monitoring and alerting
- Implement proper secret management
- Regular backup of logs and data
- Monitor API rate limits

## Contributing

1. Fork the repository
2. Create a feature branch
3. Write tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## Disclaimer

This software is for educational and research purposes only. Trading involves substantial risk of loss and is not suitable for all investors. Past performance does not guarantee future results. Use at your own risk.

## License

MIT License - see LICENSE file for details.
