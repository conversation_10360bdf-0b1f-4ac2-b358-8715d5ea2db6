"""Unit tests for strategy components."""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from src.strategies import (
    TTMSqueezeStrategy, EMACrossoverStrategy, ATRBreakoutStrategy,
    StrategyEngine, Signal, SignalType, TechnicalIndicators
)
from src.data_feed import MarketData, OHLCV


class TestTechnicalIndicators:
    """Test cases for technical indicators."""
    
    def test_sma(self):
        """Test Simple Moving Average."""
        data = pd.Series([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])
        sma_5 = TechnicalIndicators.sma(data, 5)
        
        # SMA of last 5 values should be (6+7+8+9+10)/5 = 8
        assert abs(sma_5.iloc[-1] - 8.0) < 0.001
    
    def test_ema(self):
        """Test Exponential Moving Average."""
        data = pd.Series([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])
        ema_5 = TechnicalIndicators.ema(data, 5)
        
        # EMA should be calculated correctly
        assert len(ema_5) == len(data)
        assert not pd.isna(ema_5.iloc[-1])
    
    def test_bollinger_bands(self):
        """Test Bollinger Bands."""
        data = pd.Series([10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20])
        upper, middle, lower = TechnicalIndicators.bollinger_bands(data, period=5, std_dev=2.0)
        
        # Middle band should be SMA
        sma = TechnicalIndicators.sma(data, 5)
        assert abs(middle.iloc[-1] - sma.iloc[-1]) < 0.001
        
        # Upper band should be above middle, lower below
        assert upper.iloc[-1] > middle.iloc[-1]
        assert lower.iloc[-1] < middle.iloc[-1]
    
    def test_rsi(self):
        """Test Relative Strength Index."""
        # Create data with clear trend
        data = pd.Series([10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20])
        rsi = TechnicalIndicators.rsi(data, period=5)
        
        # RSI should be between 0 and 100
        assert 0 <= rsi.iloc[-1] <= 100
    
    def test_atr(self):
        """Test Average True Range."""
        high = pd.Series([12, 13, 14, 15, 16])
        low = pd.Series([10, 11, 12, 13, 14])
        close = pd.Series([11, 12, 13, 14, 15])
        
        atr = TechnicalIndicators.atr(high, low, close, period=3)
        
        # ATR should be positive
        assert atr.iloc[-1] > 0
    
    def test_ttm_squeeze(self):
        """Test TTM Squeeze indicator."""
        # Create sample data
        high = pd.Series([102, 103, 104, 105, 106] * 10)
        low = pd.Series([98, 99, 100, 101, 102] * 10)
        close = pd.Series([100, 101, 102, 103, 104] * 10)
        
        squeeze_on, momentum = TechnicalIndicators.ttm_squeeze(high, low, close, length=20)
        
        # Should return boolean series and momentum values
        assert len(squeeze_on) == len(close)
        assert len(momentum) == len(close)


class TestTTMSqueezeStrategy:
    """Test cases for TTM Squeeze strategy."""
    
    def test_strategy_initialization(self, sample_symbols):
        """Test strategy initialization."""
        strategy = TTMSqueezeStrategy(sample_symbols)
        
        assert strategy.name == "TTM_Squeeze"
        assert strategy.symbols == sample_symbols
        assert strategy.is_active
        assert len(strategy.data_history) == 0
    
    def test_update_data(self, sample_symbols, sample_market_data):
        """Test data update."""
        strategy = TTMSqueezeStrategy(sample_symbols)
        strategy.update_data(sample_market_data)
        
        # Check that data was added for symbols in market data
        for symbol in sample_market_data.get_symbols():
            if symbol in sample_symbols:
                assert symbol in strategy.data_history
                assert len(strategy.data_history[symbol]) == 1
    
    def test_generate_signals_insufficient_data(self, sample_symbols, sample_market_data):
        """Test signal generation with insufficient data."""
        strategy = TTMSqueezeStrategy(sample_symbols)
        
        # With no historical data, should return no signals
        result = strategy.generate_signals(sample_market_data)
        
        assert result.strategy_name == "TTM_Squeeze"
        assert len(result.signals) == 0
    
    def test_strategy_deactivation(self, sample_symbols, sample_market_data):
        """Test strategy deactivation."""
        strategy = TTMSqueezeStrategy(sample_symbols)
        strategy.deactivate()
        
        result = strategy.generate_signals(sample_market_data)
        assert len(result.signals) == 0


class TestEMACrossoverStrategy:
    """Test cases for EMA Crossover strategy."""
    
    def test_strategy_initialization(self, sample_symbols):
        """Test strategy initialization."""
        strategy = EMACrossoverStrategy(sample_symbols)
        
        assert strategy.name == "EMA_Crossover"
        assert strategy.symbols == sample_symbols
        assert strategy.is_active
    
    def test_parameter_access(self, sample_symbols):
        """Test parameter access."""
        custom_params = {'fast_period': 10, 'slow_period': 20}
        strategy = EMACrossoverStrategy(sample_symbols, custom_params)
        
        assert strategy.get_parameter('fast_period') == 10
        assert strategy.get_parameter('slow_period') == 20
        assert strategy.get_parameter('nonexistent', 'default') == 'default'


class TestATRBreakoutStrategy:
    """Test cases for ATR Breakout strategy."""
    
    def test_strategy_initialization(self, sample_symbols):
        """Test strategy initialization."""
        strategy = ATRBreakoutStrategy(sample_symbols)
        
        assert strategy.name == "ATR_Breakout"
        assert strategy.symbols == sample_symbols
        assert strategy.is_active
    
    def test_breakout_level_tracking(self, sample_symbols):
        """Test breakout level tracking."""
        strategy = ATRBreakoutStrategy(sample_symbols)
        
        # Initially no levels should be set
        assert len(strategy.resistance_levels) == 0
        assert len(strategy.support_levels) == 0


class TestStrategyEngine:
    """Test cases for Strategy Engine."""
    
    def test_engine_initialization(self):
        """Test engine initialization."""
        engine = StrategyEngine()
        
        assert len(engine.strategies) == 0
        assert len(engine.latest_results) == 0
        assert engine.total_signals_generated == 0
    
    def test_add_strategy(self, sample_symbols):
        """Test adding strategies."""
        engine = StrategyEngine()
        strategy = TTMSqueezeStrategy(sample_symbols)
        
        engine.add_strategy(strategy)
        
        assert len(engine.strategies) == 1
        assert "TTM_Squeeze" in engine.strategies
        assert engine.get_strategy("TTM_Squeeze") == strategy
    
    def test_add_duplicate_strategy(self, sample_symbols):
        """Test adding duplicate strategy."""
        engine = StrategyEngine()
        strategy1 = TTMSqueezeStrategy(sample_symbols)
        strategy2 = TTMSqueezeStrategy(sample_symbols)
        
        engine.add_strategy(strategy1)
        
        # Should raise error for duplicate name
        with pytest.raises(ValueError):
            engine.add_strategy(strategy2)
        
        # Should work with replace_existing=True
        engine.add_strategy(strategy2, replace_existing=True)
        assert engine.get_strategy("TTM_Squeeze") == strategy2
    
    def test_remove_strategy(self, sample_symbols):
        """Test removing strategies."""
        engine = StrategyEngine()
        strategy = TTMSqueezeStrategy(sample_symbols)
        
        engine.add_strategy(strategy)
        assert len(engine.strategies) == 1
        
        engine.remove_strategy("TTM_Squeeze")
        assert len(engine.strategies) == 0
        assert engine.get_strategy("TTM_Squeeze") is None
    
    def test_strategy_activation(self, sample_symbols):
        """Test strategy activation/deactivation."""
        engine = StrategyEngine()
        strategy = TTMSqueezeStrategy(sample_symbols)
        
        engine.add_strategy(strategy)
        
        # Initially active
        assert strategy.is_active
        
        # Deactivate
        engine.deactivate_strategy("TTM_Squeeze")
        assert not strategy.is_active
        
        # Reactivate
        engine.activate_strategy("TTM_Squeeze")
        assert strategy.is_active
    
    def test_execute_strategies(self, sample_symbols, sample_market_data):
        """Test strategy execution."""
        engine = StrategyEngine()
        strategy = TTMSqueezeStrategy(sample_symbols)
        
        engine.add_strategy(strategy)
        
        # Execute strategies
        signals = engine.execute_strategies(sample_market_data)
        
        # Should return list of signals (may be empty due to insufficient data)
        assert isinstance(signals, list)
        assert engine.last_execution_time == sample_market_data.timestamp
    
    def test_create_default_strategies(self, sample_symbols):
        """Test creating default strategies."""
        engine = StrategyEngine()
        strategies = engine.create_default_strategies(sample_symbols)
        
        assert len(strategies) == 3  # TTM, EMA, ATR strategies
        assert 'ttm_squeeze' in strategies
        assert 'ema_crossover' in strategies
        assert 'atr_breakout' in strategies
        
        # All strategies should be added to engine
        assert len(engine.strategies) == 3
    
    def test_signal_filtering(self, sample_symbols):
        """Test signal filtering logic."""
        engine = StrategyEngine()
        
        # Create conflicting signals
        signals = [
            Signal(
                symbol='AAPL',
                signal_type=SignalType.BUY,
                timestamp=datetime.now(),
                price=150.0,
                confidence=0.8
            ),
            Signal(
                symbol='AAPL',
                signal_type=SignalType.SELL,
                timestamp=datetime.now(),
                price=150.0,
                confidence=0.6
            )
        ]
        
        filtered = engine._filter_signals(signals)
        
        # Should keep only the higher confidence signal
        assert len(filtered) == 1
        assert filtered[0].signal_type == SignalType.BUY
        assert filtered[0].confidence == 0.8
    
    def test_performance_stats(self, sample_symbols):
        """Test performance statistics."""
        engine = StrategyEngine()
        engine.create_default_strategies(sample_symbols)
        
        stats = engine.get_performance_stats()
        
        assert 'total_strategies' in stats
        assert 'active_strategies' in stats
        assert 'total_signals_generated' in stats
        assert stats['total_strategies'] == 3
        assert stats['active_strategies'] == 3
    
    def test_reset_strategies(self, sample_symbols, sample_market_data):
        """Test resetting all strategies."""
        engine = StrategyEngine()
        strategy = TTMSqueezeStrategy(sample_symbols)
        engine.add_strategy(strategy)
        
        # Add some data and execute
        engine.execute_strategies(sample_market_data)
        
        # Reset
        engine.reset_all_strategies()
        
        assert engine.total_signals_generated == 0
        assert len(engine.all_signals) == 0
        assert len(engine.latest_results) == 0


class TestSignal:
    """Test cases for Signal class."""
    
    def test_signal_creation(self):
        """Test signal creation."""
        signal = Signal(
            symbol='AAPL',
            signal_type=SignalType.BUY,
            timestamp=datetime.now(),
            price=150.0,
            confidence=0.8,
            quantity=100
        )
        
        assert signal.symbol == 'AAPL'
        assert signal.signal_type == SignalType.BUY
        assert signal.price == 150.0
        assert signal.confidence == 0.8
        assert signal.quantity == 100
    
    def test_signal_to_order_args(self):
        """Test converting signal to order arguments."""
        signal = Signal(
            symbol='AAPL',
            signal_type=SignalType.BUY,
            timestamp=datetime.now(),
            price=150.0,
            quantity=100
        )
        
        order_args = signal.to_order_args()
        
        assert order_args['symbol'] == 'AAPL'
        assert order_args['quantity'] == 100
        assert 'side' in order_args
        assert 'order_type' in order_args
