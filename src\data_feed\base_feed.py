"""Base data feed interface and data structures."""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from typing import Dict, List, Optional, Any, Iterator, Union
import pandas as pd


@dataclass
class OHLCV:
    """OHLCV (Open, High, Low, Close, Volume) data structure."""
    timestamp: datetime
    symbol: str
    open: float
    high: float
    low: float
    close: float
    volume: int
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'timestamp': self.timestamp,
            'symbol': self.symbol,
            'open': self.open,
            'high': self.high,
            'low': self.low,
            'close': self.close,
            'volume': self.volume
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'OHLCV':
        """Create from dictionary."""
        return cls(
            timestamp=data['timestamp'],
            symbol=data['symbol'],
            open=float(data['open']),
            high=float(data['high']),
            low=float(data['low']),
            close=float(data['close']),
            volume=int(data['volume'])
        )


@dataclass
class MarketData:
    """Market data container for multiple symbols."""
    timestamp: datetime
    data: Dict[str, OHLCV]
    
    def get_symbol_data(self, symbol: str) -> Optional[OHLCV]:
        """Get data for a specific symbol."""
        return self.data.get(symbol)
    
    def get_symbols(self) -> List[str]:
        """Get list of symbols in this market data."""
        return list(self.data.keys())
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'timestamp': self.timestamp,
            'data': {symbol: ohlcv.to_dict() for symbol, ohlcv in self.data.items()}
        }


class BaseFeed(ABC):
    """Abstract base class for data feeds."""
    
    def __init__(self, symbols: List[str]):
        """Initialize data feed.
        
        Args:
            symbols: List of symbols to track
        """
        self.symbols = symbols
        self.is_running = False
        self.current_data: Optional[MarketData] = None
        
    @abstractmethod
    def connect(self) -> bool:
        """Connect to data source."""
        pass
    
    @abstractmethod
    def disconnect(self):
        """Disconnect from data source."""
        pass
    
    @abstractmethod
    def is_connected(self) -> bool:
        """Check if connected to data source."""
        pass
    
    @abstractmethod
    def get_latest_data(self, symbol: str) -> Optional[OHLCV]:
        """Get latest data for a symbol."""
        pass
    
    @abstractmethod
    def get_historical_data(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        interval: str = '1d'
    ) -> pd.DataFrame:
        """Get historical data for a symbol."""
        pass
    
    @abstractmethod
    def stream(self) -> Iterator[MarketData]:
        """Stream market data."""
        pass
    
    def start(self):
        """Start the data feed."""
        if self.connect():
            self.is_running = True
    
    def stop(self):
        """Stop the data feed."""
        self.is_running = False
        self.disconnect()
    
    def add_symbol(self, symbol: str):
        """Add a symbol to track."""
        if symbol not in self.symbols:
            self.symbols.append(symbol)
    
    def remove_symbol(self, symbol: str):
        """Remove a symbol from tracking."""
        if symbol in self.symbols:
            self.symbols.remove(symbol)
    
    def get_symbols(self) -> List[str]:
        """Get list of tracked symbols."""
        return self.symbols.copy()


class DataFeedManager:
    """Manager for multiple data feeds."""
    
    def __init__(self):
        self.feeds: Dict[str, BaseFeed] = {}
        self.primary_feed: Optional[str] = None
    
    def add_feed(self, name: str, feed: BaseFeed, is_primary: bool = False):
        """Add a data feed.
        
        Args:
            name: Name of the feed
            feed: Feed instance
            is_primary: Whether this is the primary feed
        """
        self.feeds[name] = feed
        if is_primary or self.primary_feed is None:
            self.primary_feed = name
    
    def remove_feed(self, name: str):
        """Remove a data feed."""
        if name in self.feeds:
            self.feeds[name].stop()
            del self.feeds[name]
            
            if self.primary_feed == name:
                self.primary_feed = next(iter(self.feeds.keys())) if self.feeds else None
    
    def get_feed(self, name: str) -> Optional[BaseFeed]:
        """Get a specific feed."""
        return self.feeds.get(name)
    
    def get_primary_feed(self) -> Optional[BaseFeed]:
        """Get the primary feed."""
        if self.primary_feed:
            return self.feeds.get(self.primary_feed)
        return None
    
    def start_all(self):
        """Start all feeds."""
        for feed in self.feeds.values():
            feed.start()
    
    def stop_all(self):
        """Stop all feeds."""
        for feed in self.feeds.values():
            feed.stop()
    
    def get_latest_data(self, symbol: str, feed_name: Optional[str] = None) -> Optional[OHLCV]:
        """Get latest data for a symbol from specified feed or primary feed."""
        if feed_name:
            feed = self.feeds.get(feed_name)
        else:
            feed = self.get_primary_feed()
        
        if feed:
            return feed.get_latest_data(symbol)
        return None
