"""Integration tests specifically for backtesting functionality."""

import pytest
import pandas as pd
from datetime import datetime, timedelta
from unittest.mock import patch

from src.orchestrator import TradingOrchestrator
from src.broker import PaperBroker
from src.data_feed import HistoricalFeed
from src.utils import config


class TestBacktestingIntegration:
    """Integration tests for backtesting functionality."""
    
    def test_complete_backtest_run(self, sample_symbols, sample_ohlcv_data, integration_test_data):
        """Test a complete backtest run from start to finish."""
        # Ensure paper mode
        with patch.object(config, 'mode', 'paper'):
            with patch.object(config, 'initial_capital', integration_test_data['initial_capital']):
                
                orchestrator = TradingOrchestrator(integration_test_data['symbols'])
                
                # Mock data loading
                with patch('src.data_feed.data_downloader.DataDownloader.list_data_files') as mock_list:
                    with patch('src.data_feed.data_downloader.DataDownloader.load_data_file') as mock_load:
                        mock_list.return_value = ['backtest_data.csv']
                        mock_load.return_value = sample_ohlcv_data
                        
                        # Initialize
                        success = orchestrator.initialize()
                        assert success
                        
                        # Record initial state
                        initial_value = orchestrator.broker.get_portfolio_value()
                        initial_cash = orchestrator.broker.get_cash_balance()
                        
                        assert initial_value == integration_test_data['initial_capital']
                        assert initial_cash == integration_test_data['initial_capital']
                        
                        # Run backtest simulation
                        primary_feed = orchestrator.data_feed_manager.get_primary_feed()
                        assert isinstance(primary_feed, HistoricalFeed)
                        
                        periods_processed = 0
                        max_periods = 50  # Limit for testing
                        
                        for market_data in primary_feed.stream():
                            orchestrator._process_market_data(market_data)
                            periods_processed += 1
                            
                            if periods_processed >= max_periods:
                                break
                        
                        # Verify backtest results
                        final_value = orchestrator.broker.get_portfolio_value()
                        total_signals = orchestrator.total_signals_processed
                        total_orders = orchestrator.total_orders_placed
                        
                        # Basic sanity checks
                        assert periods_processed > 0
                        assert final_value > 0
                        assert total_signals >= 0
                        assert total_orders >= 0
                        
                        # Check that some activity occurred
                        if total_orders > 0:
                            # If orders were placed, portfolio should have changed
                            assert final_value != initial_value or orchestrator.broker.get_cash_balance() != initial_cash
                        
                        # Get performance stats if available
                        if hasattr(orchestrator.broker, 'get_performance_stats'):
                            perf_stats = orchestrator.broker.get_performance_stats()
                            
                            assert 'initial_capital' in perf_stats
                            assert 'current_value' in perf_stats
                            assert 'total_return_pct' in perf_stats
                            
                            # Verify initial capital matches
                            assert perf_stats['initial_capital'] == integration_test_data['initial_capital']
                        
                        orchestrator.stop()
    
    def test_backtest_with_multiple_strategies(self, sample_symbols, sample_ohlcv_data):
        """Test backtest with multiple active strategies."""
        with patch.object(config, 'mode', 'paper'):
            orchestrator = TradingOrchestrator(sample_symbols)
            
            with patch('src.data_feed.data_downloader.DataDownloader.list_data_files') as mock_list:
                with patch('src.data_feed.data_downloader.DataDownloader.load_data_file') as mock_load:
                    mock_list.return_value = ['test_data.csv']
                    mock_load.return_value = sample_ohlcv_data
                    
                    success = orchestrator.initialize()
                    assert success
                    
                    # Verify multiple strategies are active
                    strategy_stats = orchestrator.strategy_engine.get_performance_stats()
                    assert strategy_stats['total_strategies'] >= 3  # TTM, EMA, ATR
                    assert strategy_stats['active_strategies'] >= 3
                    
                    # Run partial backtest
                    primary_feed = orchestrator.data_feed_manager.get_primary_feed()
                    periods = 0
                    
                    for market_data in primary_feed.stream():
                        orchestrator._process_market_data(market_data)
                        periods += 1
                        
                        if periods >= 20:
                            break
                    
                    # Check that strategies generated results
                    latest_results = orchestrator.strategy_engine.get_latest_results()
                    assert len(latest_results) > 0
                    
                    # Each active strategy should have a result
                    for strategy_name in orchestrator.strategy_engine.list_strategies():
                        strategy = orchestrator.strategy_engine.get_strategy(strategy_name)
                        if strategy.is_active:
                            assert strategy_name in latest_results
                    
                    orchestrator.stop()
    
    def test_backtest_risk_management(self, sample_symbols, sample_ohlcv_data):
        """Test risk management during backtesting."""
        with patch.object(config, 'mode', 'paper'):
            with patch.object(config, 'max_daily_drawdown', 0.05):  # 5% max drawdown
                
                orchestrator = TradingOrchestrator(sample_symbols)
                
                with patch('src.data_feed.data_downloader.DataDownloader.list_data_files') as mock_list:
                    with patch('src.data_feed.data_downloader.DataDownloader.load_data_file') as mock_load:
                        mock_list.return_value = ['test_data.csv']
                        mock_load.return_value = sample_ohlcv_data
                        
                        success = orchestrator.initialize()
                        assert success
                        
                        # Track risk metrics during backtest
                        initial_value = orchestrator.broker.get_portfolio_value()
                        min_value = initial_value
                        max_value = initial_value
                        
                        primary_feed = orchestrator.data_feed_manager.get_primary_feed()
                        periods = 0
                        
                        for market_data in primary_feed.stream():
                            orchestrator._process_market_data(market_data)
                            
                            current_value = orchestrator.broker.get_portfolio_value()
                            min_value = min(min_value, current_value)
                            max_value = max(max_value, current_value)
                            
                            periods += 1
                            if periods >= 30:
                                break
                        
                        # Check risk management status
                        risk_status = orchestrator.risk_manager.get_risk_status()
                        
                        assert 'emergency_stop_active' in risk_status
                        assert 'portfolio_metrics' in risk_status
                        
                        # Verify drawdown tracking
                        portfolio_metrics = risk_status['portfolio_metrics']
                        if 'max_drawdown_pct' in portfolio_metrics:
                            max_drawdown = portfolio_metrics['max_drawdown_pct']
                            # Drawdown should be reasonable for test data
                            assert max_drawdown >= 0
                        
                        orchestrator.stop()
    
    def test_backtest_performance_metrics(self, sample_symbols, sample_ohlcv_data):
        """Test performance metrics calculation during backtest."""
        with patch.object(config, 'mode', 'paper'):
            orchestrator = TradingOrchestrator(sample_symbols)
            
            with patch('src.data_feed.data_downloader.DataDownloader.list_data_files') as mock_list:
                with patch('src.data_feed.data_downloader.DataDownloader.load_data_file') as mock_load:
                    mock_list.return_value = ['test_data.csv']
                    mock_load.return_value = sample_ohlcv_data
                    
                    success = orchestrator.initialize()
                    assert success
                    
                    # Run backtest
                    primary_feed = orchestrator.data_feed_manager.get_primary_feed()
                    periods = 0
                    
                    for market_data in primary_feed.stream():
                        orchestrator._process_market_data(market_data)
                        periods += 1
                        
                        if periods >= 25:
                            break
                    
                    # Get comprehensive performance metrics
                    if hasattr(orchestrator.broker, 'get_performance_stats'):
                        perf_stats = orchestrator.broker.get_performance_stats()
                        
                        # Check required metrics
                        required_metrics = [
                            'initial_capital', 'current_value', 'total_return_pct',
                            'total_trades', 'unrealized_pnl', 'realized_pnl'
                        ]
                        
                        for metric in required_metrics:
                            assert metric in perf_stats
                        
                        # Verify metric values are reasonable
                        assert perf_stats['initial_capital'] > 0
                        assert perf_stats['current_value'] > 0
                        assert isinstance(perf_stats['total_trades'], int)
                        assert perf_stats['total_trades'] >= 0
                    
                    # Check strategy performance
                    strategy_stats = orchestrator.strategy_engine.get_performance_stats()
                    assert strategy_stats['total_signals_generated'] >= 0
                    
                    # Check risk metrics
                    risk_status = orchestrator.risk_manager.get_risk_status()
                    portfolio_metrics = risk_status.get('portfolio_metrics', {})
                    
                    if portfolio_metrics:
                        assert 'total_value' in portfolio_metrics
                        assert portfolio_metrics['total_value'] > 0
                    
                    orchestrator.stop()
    
    def test_backtest_data_integrity(self, sample_symbols, sample_ohlcv_data):
        """Test data integrity throughout backtest."""
        with patch.object(config, 'mode', 'paper'):
            orchestrator = TradingOrchestrator(sample_symbols)
            
            with patch('src.data_feed.data_downloader.DataDownloader.list_data_files') as mock_list:
                with patch('src.data_feed.data_downloader.DataDownloader.load_data_file') as mock_load:
                    mock_list.return_value = ['test_data.csv']
                    mock_load.return_value = sample_ohlcv_data
                    
                    success = orchestrator.initialize()
                    assert success
                    
                    # Track data consistency
                    primary_feed = orchestrator.data_feed_manager.get_primary_feed()
                    assert isinstance(primary_feed, HistoricalFeed)
                    
                    previous_timestamp = None
                    periods = 0
                    
                    for market_data in primary_feed.stream():
                        # Verify timestamp progression
                        if previous_timestamp:
                            assert market_data.timestamp >= previous_timestamp
                        
                        # Verify market data structure
                        assert len(market_data.data) > 0
                        
                        for symbol, ohlcv in market_data.data.items():
                            assert symbol in sample_symbols
                            assert ohlcv.open > 0
                            assert ohlcv.high >= ohlcv.open
                            assert ohlcv.low <= ohlcv.open
                            assert ohlcv.close > 0
                            assert ohlcv.volume >= 0
                        
                        # Process the data
                        orchestrator._process_market_data(market_data)
                        
                        previous_timestamp = market_data.timestamp
                        periods += 1
                        
                        if periods >= 15:
                            break
                    
                    # Verify final state consistency
                    final_status = orchestrator.get_status()
                    assert final_status['last_update'] is not None
                    
                    # Portfolio value should be consistent
                    portfolio_value = orchestrator.broker.get_portfolio_value()
                    cash_balance = orchestrator.broker.get_cash_balance()
                    positions = orchestrator.broker.get_positions()
                    
                    positions_value = sum(pos.market_value for pos in positions.values())
                    calculated_total = cash_balance + positions_value
                    
                    # Allow small rounding differences
                    assert abs(portfolio_value - calculated_total) < 0.01
                    
                    orchestrator.stop()
    
    def test_backtest_error_recovery(self, sample_symbols):
        """Test error recovery during backtest."""
        with patch.object(config, 'mode', 'paper'):
            orchestrator = TradingOrchestrator(sample_symbols)
            
            # Test with missing data files
            with patch('src.data_feed.data_downloader.DataDownloader.list_data_files') as mock_list:
                mock_list.return_value = []  # No data files
                
                # Should still initialize and generate synthetic data
                success = orchestrator.initialize()
                assert success
                
                # Should have created synthetic data
                primary_feed = orchestrator.data_feed_manager.get_primary_feed()
                assert primary_feed is not None
                
                orchestrator.stop()
    
    def test_backtest_memory_usage(self, sample_symbols, sample_ohlcv_data):
        """Test memory usage during extended backtest."""
        with patch.object(config, 'mode', 'paper'):
            orchestrator = TradingOrchestrator(sample_symbols)
            
            with patch('src.data_feed.data_downloader.DataDownloader.list_data_files') as mock_list:
                with patch('src.data_feed.data_downloader.DataDownloader.load_data_file') as mock_load:
                    mock_list.return_value = ['test_data.csv']
                    mock_load.return_value = sample_ohlcv_data
                    
                    success = orchestrator.initialize()
                    assert success
                    
                    # Run longer backtest to test memory management
                    primary_feed = orchestrator.data_feed_manager.get_primary_feed()
                    periods = 0
                    
                    for market_data in primary_feed.stream():
                        orchestrator._process_market_data(market_data)
                        periods += 1
                        
                        # Check memory usage periodically
                        if periods % 10 == 0:
                            # Verify that data structures aren't growing unbounded
                            strategy_engine = orchestrator.strategy_engine
                            
                            # Check signal history limits
                            total_signals = len(strategy_engine.all_signals)
                            assert total_signals <= 1000  # Should be capped
                            
                            # Check strategy data history limits
                            for strategy in strategy_engine.strategies.values():
                                for symbol_data in strategy.data_history.values():
                                    assert len(symbol_data) <= 1000  # Should be capped
                        
                        if periods >= 100:  # Extended test
                            break
                    
                    orchestrator.stop()
