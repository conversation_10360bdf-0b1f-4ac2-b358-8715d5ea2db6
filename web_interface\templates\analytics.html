{% extends "base.html" %}

{% block title %}Analytics - Robinhood Trading Engine{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h2><i class="fas fa-chart-bar me-2"></i>Performance Analytics</h2>
        <p class="text-muted">Detailed analysis of your trading performance and strategy effectiveness.</p>
    </div>
</div>

<!-- Performance Metrics -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value" id="total-return">0.00%</div>
                <div class="metric-label">Total Return</div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value" id="sharpe-ratio">0.00</div>
                <div class="metric-label">Sharpe Ratio</div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value" id="max-drawdown">0.00%</div>
                <div class="metric-label">Max Drawdown</div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value" id="win-rate">0.00%</div>
                <div class="metric-label">Win Rate</div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value" id="profit-factor">0.00</div>
                <div class="metric-label">Profit Factor</div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value" id="total-trades">0</div>
                <div class="metric-label">Total Trades</div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row 1 -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Equity Curve</h5>
                <div class="btn-group btn-group-sm" role="group">
                    <input type="radio" class="btn-check" name="timeframe" id="timeframe-1d" checked>
                    <label class="btn btn-outline-primary" for="timeframe-1d">1D</label>
                    
                    <input type="radio" class="btn-check" name="timeframe" id="timeframe-1w">
                    <label class="btn btn-outline-primary" for="timeframe-1w">1W</label>
                    
                    <input type="radio" class="btn-check" name="timeframe" id="timeframe-1m">
                    <label class="btn btn-outline-primary" for="timeframe-1m">1M</label>
                    
                    <input type="radio" class="btn-check" name="timeframe" id="timeframe-all">
                    <label class="btn btn-outline-primary" for="timeframe-all">All</label>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="equity-curve-chart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Strategy Performance</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="strategy-performance-chart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row 2 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Monthly Returns</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="monthly-returns-chart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-area me-2"></i>Drawdown Analysis</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="drawdown-chart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Strategy Analysis -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-brain me-2"></i>Strategy Analysis</h5>
            </div>
            <div class="card-body">
                <div id="strategy-analysis-table">
                    <div class="loading-spinner">
                        <div class="spinner-border text-primary" role="status"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Trade History -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent Trades</h5>
                <button class="btn btn-sm btn-outline-primary" onclick="exportTradeHistory()">
                    <i class="fas fa-download me-1"></i>Export CSV
                </button>
            </div>
            <div class="card-body">
                <div id="trade-history-table">
                    <div class="loading-spinner">
                        <div class="spinner-border text-primary" role="status"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Risk Metrics -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Risk Metrics</h5>
            </div>
            <div class="card-body">
                <div id="risk-metrics">
                    <div class="loading-spinner">
                        <div class="spinner-border text-primary" role="status"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Volatility Analysis</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="volatility-chart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Global variables for charts
    let equityCurveChart;
    let strategyPerformanceChart;
    let monthlyReturnsChart;
    let drawdownChart;
    let volatilityChart;
    
    // Sample data for demonstration
    let performanceData = {
        equity_curve: [],
        monthly_returns: [],
        drawdown_data: [],
        strategy_performance: {},
        risk_metrics: {},
        trade_history: []
    };

    // Initialize analytics page
    function loadInitialData() {
        loadPerformanceData();
        initializeCharts();
        loadStrategyAnalysis();
        loadTradeHistory();
        loadRiskMetrics();
    }

    // Load performance data
    async function loadPerformanceData() {
        try {
            // In a real implementation, this would fetch from the API
            // For now, we'll generate sample data
            generateSampleData();
            updatePerformanceMetrics();
        } catch (error) {
            console.error('Error loading performance data:', error);
        }
    }

    // Generate sample data for demonstration
    function generateSampleData() {
        const startDate = new Date();
        startDate.setMonth(startDate.getMonth() - 6);
        
        let portfolioValue = 10000;
        let peak = portfolioValue;
        
        for (let i = 0; i < 180; i++) {
            const date = new Date(startDate);
            date.setDate(date.getDate() + i);
            
            // Generate random daily return
            const dailyReturn = (Math.random() - 0.48) * 0.02; // Slightly positive bias
            portfolioValue *= (1 + dailyReturn);
            
            peak = Math.max(peak, portfolioValue);
            const drawdown = (peak - portfolioValue) / peak * 100;
            
            performanceData.equity_curve.push({
                date: date.toISOString().split('T')[0],
                value: portfolioValue,
                return: dailyReturn * 100
            });
            
            performanceData.drawdown_data.push({
                date: date.toISOString().split('T')[0],
                drawdown: drawdown
            });
        }
        
        // Generate monthly returns
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
        performanceData.monthly_returns = months.map(month => ({
            month: month,
            return: (Math.random() - 0.4) * 10 // -4% to 6% range
        }));
        
        // Strategy performance
        performanceData.strategy_performance = {
            'TTM Squeeze': 35,
            'EMA Crossover': 28,
            'ATR Breakout': 37
        };
        
        // Risk metrics
        performanceData.risk_metrics = {
            total_return: 12.5,
            sharpe_ratio: 1.23,
            max_drawdown: 8.2,
            win_rate: 58.3,
            profit_factor: 1.45,
            total_trades: 47,
            volatility: 15.6,
            var_95: -2.1,
            calmar_ratio: 1.52
        };
    }

    // Update performance metrics display
    function updatePerformanceMetrics() {
        const metrics = performanceData.risk_metrics;
        
        document.getElementById('total-return').textContent = `${metrics.total_return.toFixed(2)}%`;
        document.getElementById('total-return').className = `metric-value ${metrics.total_return >= 0 ? 'positive' : 'negative'}`;
        
        document.getElementById('sharpe-ratio').textContent = metrics.sharpe_ratio.toFixed(2);
        document.getElementById('sharpe-ratio').className = `metric-value ${metrics.sharpe_ratio >= 1 ? 'positive' : 'neutral'}`;
        
        document.getElementById('max-drawdown').textContent = `${metrics.max_drawdown.toFixed(2)}%`;
        document.getElementById('max-drawdown').className = 'metric-value negative';
        
        document.getElementById('win-rate').textContent = `${metrics.win_rate.toFixed(1)}%`;
        document.getElementById('win-rate').className = `metric-value ${metrics.win_rate >= 50 ? 'positive' : 'negative'}`;
        
        document.getElementById('profit-factor').textContent = metrics.profit_factor.toFixed(2);
        document.getElementById('profit-factor').className = `metric-value ${metrics.profit_factor >= 1 ? 'positive' : 'negative'}`;
        
        document.getElementById('total-trades').textContent = metrics.total_trades;
        document.getElementById('total-trades').className = 'metric-value neutral';
    }

    // Initialize all charts
    function initializeCharts() {
        initializeEquityCurveChart();
        initializeStrategyPerformanceChart();
        initializeMonthlyReturnsChart();
        initializeDrawdownChart();
        initializeVolatilityChart();
    }

    // Initialize equity curve chart
    function initializeEquityCurveChart() {
        const ctx = document.getElementById('equity-curve-chart').getContext('2d');
        
        equityCurveChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: performanceData.equity_curve.map(d => d.date),
                datasets: [{
                    label: 'Portfolio Value',
                    data: performanceData.equity_curve.map(d => d.value),
                    borderColor: '#00d4aa',
                    backgroundColor: 'rgba(0, 212, 170, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: '#fff' }
                    }
                },
                scales: {
                    x: { 
                        ticks: { color: '#fff' },
                        grid: { color: '#333' }
                    },
                    y: { 
                        ticks: { 
                            color: '#fff',
                            callback: function(value) {
                                return formatCurrency(value);
                            }
                        },
                        grid: { color: '#333' }
                    }
                }
            }
        });
    }

    // Initialize strategy performance chart
    function initializeStrategyPerformanceChart() {
        const ctx = document.getElementById('strategy-performance-chart').getContext('2d');
        
        const strategies = Object.keys(performanceData.strategy_performance);
        const values = Object.values(performanceData.strategy_performance);
        
        strategyPerformanceChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: strategies,
                datasets: [{
                    data: values,
                    backgroundColor: ['#00d4aa', '#ff6b6b', '#4ecdc4']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: '#fff' }
                    }
                }
            }
        });
    }

    // Initialize monthly returns chart
    function initializeMonthlyReturnsChart() {
        const ctx = document.getElementById('monthly-returns-chart').getContext('2d');
        
        monthlyReturnsChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: performanceData.monthly_returns.map(d => d.month),
                datasets: [{
                    label: 'Monthly Return (%)',
                    data: performanceData.monthly_returns.map(d => d.return),
                    backgroundColor: performanceData.monthly_returns.map(d => 
                        d.return >= 0 ? '#00d4aa' : '#ff6b6b'
                    )
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: '#fff' }
                    }
                },
                scales: {
                    x: { 
                        ticks: { color: '#fff' },
                        grid: { color: '#333' }
                    },
                    y: { 
                        ticks: { 
                            color: '#fff',
                            callback: function(value) {
                                return value.toFixed(1) + '%';
                            }
                        },
                        grid: { color: '#333' }
                    }
                }
            }
        });
    }

    // Initialize drawdown chart
    function initializeDrawdownChart() {
        const ctx = document.getElementById('drawdown-chart').getContext('2d');
        
        drawdownChart = new Chart(ctx, {
            type: 'area',
            data: {
                labels: performanceData.drawdown_data.map(d => d.date),
                datasets: [{
                    label: 'Drawdown (%)',
                    data: performanceData.drawdown_data.map(d => -d.drawdown),
                    borderColor: '#ff6b6b',
                    backgroundColor: 'rgba(255, 107, 107, 0.2)',
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: '#fff' }
                    }
                },
                scales: {
                    x: { 
                        ticks: { color: '#fff' },
                        grid: { color: '#333' }
                    },
                    y: { 
                        ticks: { 
                            color: '#fff',
                            callback: function(value) {
                                return value.toFixed(1) + '%';
                            }
                        },
                        grid: { color: '#333' }
                    }
                }
            }
        });
    }

    // Initialize volatility chart
    function initializeVolatilityChart() {
        const ctx = document.getElementById('volatility-chart').getContext('2d');
        
        // Generate sample volatility data
        const volatilityData = [];
        for (let i = 0; i < 30; i++) {
            volatilityData.push({
                date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                volatility: 10 + Math.random() * 15
            });
        }
        
        volatilityChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: volatilityData.map(d => d.date),
                datasets: [{
                    label: 'Rolling Volatility (%)',
                    data: volatilityData.map(d => d.volatility),
                    borderColor: '#f9ca24',
                    backgroundColor: 'rgba(249, 202, 36, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: '#fff' }
                    }
                },
                scales: {
                    x: { 
                        ticks: { color: '#fff' },
                        grid: { color: '#333' }
                    },
                    y: { 
                        ticks: { 
                            color: '#fff',
                            callback: function(value) {
                                return value.toFixed(1) + '%';
                            }
                        },
                        grid: { color: '#333' }
                    }
                }
            }
        });
    }

    // Load strategy analysis
    function loadStrategyAnalysis() {
        const strategies = [
            { name: 'TTM Squeeze', signals: 15, success_rate: 66.7, avg_return: 2.3, max_drawdown: 4.1 },
            { name: 'EMA Crossover', signals: 12, success_rate: 58.3, avg_return: 1.8, max_drawdown: 3.2 },
            { name: 'ATR Breakout', signals: 20, success_rate: 55.0, avg_return: 2.1, max_drawdown: 5.8 }
        ];
        
        const tableHtml = `
            <div class="table-responsive">
                <table class="table table-dark">
                    <thead>
                        <tr>
                            <th>Strategy</th>
                            <th>Signals</th>
                            <th>Success Rate</th>
                            <th>Avg Return</th>
                            <th>Max Drawdown</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${strategies.map(strategy => `
                            <tr>
                                <td><strong>${strategy.name}</strong></td>
                                <td>${strategy.signals}</td>
                                <td class="${strategy.success_rate >= 60 ? 'positive' : strategy.success_rate >= 50 ? 'neutral' : 'negative'}">
                                    ${strategy.success_rate.toFixed(1)}%
                                </td>
                                <td class="${strategy.avg_return >= 0 ? 'positive' : 'negative'}">
                                    ${strategy.avg_return.toFixed(1)}%
                                </td>
                                <td class="negative">${strategy.max_drawdown.toFixed(1)}%</td>
                                <td><span class="badge bg-success">Active</span></td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
        
        document.getElementById('strategy-analysis-table').innerHTML = tableHtml;
    }

    // Load trade history
    function loadTradeHistory() {
        // Sample trade data
        const trades = [
            { symbol: 'AAPL', side: 'BUY', quantity: 100, price: 150.25, date: '2024-01-15', pnl: 250.00 },
            { symbol: 'GOOGL', side: 'SELL', quantity: 50, price: 2750.80, date: '2024-01-14', pnl: -125.50 },
            { symbol: 'MSFT', side: 'BUY', quantity: 75, price: 380.45, date: '2024-01-13', pnl: 180.75 },
            { symbol: 'TSLA', side: 'SELL', quantity: 25, price: 245.60, date: '2024-01-12', pnl: 95.25 },
            { symbol: 'SPY', side: 'BUY', quantity: 200, price: 485.30, date: '2024-01-11', pnl: 320.00 }
        ];
        
        const tableHtml = `
            <div class="table-responsive">
                <table class="table table-dark table-sm">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Symbol</th>
                            <th>Side</th>
                            <th>Quantity</th>
                            <th>Price</th>
                            <th>P&L</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${trades.map(trade => `
                            <tr>
                                <td>${trade.date}</td>
                                <td><strong>${trade.symbol}</strong></td>
                                <td>
                                    <span class="badge ${trade.side === 'BUY' ? 'bg-success' : 'bg-danger'}">
                                        ${trade.side}
                                    </span>
                                </td>
                                <td>${trade.quantity}</td>
                                <td>${formatCurrency(trade.price)}</td>
                                <td class="${trade.pnl >= 0 ? 'positive' : 'negative'}">
                                    ${formatCurrency(trade.pnl)}
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
        
        document.getElementById('trade-history-table').innerHTML = tableHtml;
    }

    // Load risk metrics
    function loadRiskMetrics() {
        const metrics = performanceData.risk_metrics;
        
        const metricsHtml = `
            <div class="row">
                <div class="col-6 mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Volatility (Annualized)</span>
                        <span class="fw-bold">${metrics.volatility.toFixed(1)}%</span>
                    </div>
                </div>
                <div class="col-6 mb-3">
                    <div class="d-flex justify-content-between">
                        <span>VaR (95%)</span>
                        <span class="fw-bold negative">${metrics.var_95.toFixed(1)}%</span>
                    </div>
                </div>
                <div class="col-6 mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Calmar Ratio</span>
                        <span class="fw-bold positive">${metrics.calmar_ratio.toFixed(2)}</span>
                    </div>
                </div>
                <div class="col-6 mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Sortino Ratio</span>
                        <span class="fw-bold positive">1.68</span>
                    </div>
                </div>
                <div class="col-12">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Risk metrics are calculated based on daily returns over the selected period.
                    </div>
                </div>
            </div>
        `;
        
        document.getElementById('risk-metrics').innerHTML = metricsHtml;
    }

    // Export trade history
    function exportTradeHistory() {
        // This would export actual trade data in a real implementation
        showAlert('Export Started', 'Trade history export has been initiated. The file will download shortly.', 'info');
    }

    // Show alert
    function showAlert(title, message, type = 'info') {
        alert(`${title}: ${message}`);
    }
</script>
{% endblock %}
