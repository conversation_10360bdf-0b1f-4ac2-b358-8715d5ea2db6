{% extends "base.html" %}

{% block title %}Dashboard - Robinhood Trading Engine{% endblock %}

{% block content %}
<!-- Engine Controls -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>Engine Controls</h5>
            </div>
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <span class="status-indicator" id="engine-status-indicator"></span>
                            <span id="engine-status-text">Unknown</span>
                            <span class="badge bg-secondary ms-2" id="engine-mode">Unknown</span>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <button class="btn btn-success me-2" id="btn-initialize" onclick="initializeEngine()">
                            <i class="fas fa-power-off me-1"></i>Initialize
                        </button>
                        <button class="btn btn-primary me-2" id="btn-start" onclick="startTrading()" disabled>
                            <i class="fas fa-play me-1"></i>Start Trading
                        </button>
                        <button class="btn btn-warning me-2" id="btn-stop" onclick="stopTrading()" disabled>
                            <i class="fas fa-stop me-1"></i>Stop Trading
                        </button>
                        <button class="btn btn-danger emergency-stop" id="btn-emergency" onclick="emergencyStop()" disabled>
                            <i class="fas fa-exclamation-triangle me-1"></i>Emergency Stop
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Portfolio Overview -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value" id="portfolio-value">$0.00</div>
                <div class="metric-label">Portfolio Value</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value" id="cash-balance">$0.00</div>
                <div class="metric-label">Cash Balance</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value" id="daily-pnl">$0.00</div>
                <div class="metric-label">Daily P&L</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value" id="total-return">0.00%</div>
                <div class="metric-label">Total Return</div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Portfolio Performance</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="portfolio-chart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-pie-chart me-2"></i>Position Allocation</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="allocation-chart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Positions and Strategies Row -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-briefcase me-2"></i>Current Positions</h5>
            </div>
            <div class="card-body">
                <div id="positions-table">
                    <div class="text-center text-muted">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <p>No positions</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-brain me-2"></i>Active Strategies</h5>
            </div>
            <div class="card-body">
                <div id="strategies-list">
                    <div class="loading-spinner">
                        <div class="spinner-border text-primary" role="status"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-signal me-2"></i>Recent Signals</h5>
            </div>
            <div class="card-body">
                <div id="recent-signals">
                    <div class="text-center text-muted">
                        <i class="fas fa-broadcast-tower fa-2x mb-2"></i>
                        <p>No recent signals</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Risk Management</h5>
            </div>
            <div class="card-body">
                <div id="risk-status">
                    <div class="loading-spinner">
                        <div class="spinner-border text-primary" role="status"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Alerts Modal -->
<div class="modal fade" id="alertModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark">
            <div class="modal-header">
                <h5 class="modal-title" id="alertModalTitle">Alert</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="alertModalBody">
                <!-- Alert content will be inserted here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Global variables for charts
    let portfolioChart;
    let allocationChart;
    let portfolioData = [];
    let currentStatus = {};

    // Initialize dashboard
    function loadInitialData() {
        loadStatus();
        loadPortfolio();
        loadStrategies();
        loadRiskStatus();
        initializeCharts();
    }

    // Load engine status
    async function loadStatus() {
        try {
            const data = await apiRequest('/api/status');
            updateEngineStatus(data);
        } catch (error) {
            console.error('Error loading status:', error);
            showError('engine-status-text', 'Error loading status');
        }
    }

    // Load portfolio data
    async function loadPortfolio() {
        try {
            const data = await apiRequest('/api/portfolio');
            updatePortfolioDisplay(data);
        } catch (error) {
            console.error('Error loading portfolio:', error);
            showError('positions-table', 'Error loading portfolio data');
        }
    }

    // Load strategies
    async function loadStrategies() {
        try {
            const data = await apiRequest('/api/strategies');
            updateStrategiesDisplay(data);
        } catch (error) {
            console.error('Error loading strategies:', error);
            showError('strategies-list', 'Error loading strategies');
        }
    }

    // Load risk status
    async function loadRiskStatus() {
        try {
            const data = await apiRequest('/api/risk');
            updateRiskDisplay(data);
        } catch (error) {
            console.error('Error loading risk status:', error);
            showError('risk-status', 'Error loading risk status');
        }
    }

    // Update engine status display
    function updateEngineStatus(data) {
        currentStatus = data;
        const indicator = document.getElementById('engine-status-indicator');
        const text = document.getElementById('engine-status-text');
        const mode = document.getElementById('engine-mode');

        const btnInitialize = document.getElementById('btn-initialize');
        const btnStart = document.getElementById('btn-start');
        const btnStop = document.getElementById('btn-stop');
        const btnEmergency = document.getElementById('btn-emergency');

        if (data.error) {
            indicator.className = 'status-indicator status-stopped';
            text.textContent = 'Error';
            mode.textContent = 'Unknown';
            return;
        }

        // Update mode
        mode.textContent = data.mode || 'Unknown';
        mode.className = `badge ${data.mode === 'live' ? 'bg-danger' : 'bg-success'}`;

        if (!data.initialized) {
            indicator.className = 'status-indicator status-stopped';
            text.textContent = 'Not Initialized';
            btnInitialize.disabled = false;
            btnStart.disabled = true;
            btnStop.disabled = true;
            btnEmergency.disabled = true;
        } else if (data.is_running) {
            indicator.className = 'status-indicator status-running';
            text.textContent = 'Running';
            btnInitialize.disabled = true;
            btnStart.disabled = true;
            btnStop.disabled = false;
            btnEmergency.disabled = false;
        } else {
            indicator.className = 'status-indicator status-warning';
            text.textContent = 'Initialized';
            btnInitialize.disabled = true;
            btnStart.disabled = false;
            btnStop.disabled = true;
            btnEmergency.disabled = true;
        }
    }

    // Update portfolio display
    function updatePortfolioDisplay(data) {
        if (data.error) {
            showError('positions-table', data.error);
            return;
        }

        // Update metrics
        document.getElementById('portfolio-value').textContent = formatCurrency(data.total_value || 0);
        document.getElementById('cash-balance').textContent = formatCurrency(data.cash_balance || 0);

        // Calculate daily P&L (simplified)
        const dailyPnl = (data.positions || []).reduce((sum, pos) => sum + (pos.unrealized_pnl || 0), 0);
        const dailyPnlElement = document.getElementById('daily-pnl');
        dailyPnlElement.textContent = formatCurrency(dailyPnl);
        dailyPnlElement.className = `metric-value ${dailyPnl >= 0 ? 'positive' : 'negative'}`;

        // Calculate total return
        const totalReturn = data.performance ? data.performance.total_return_pct || 0 : 0;
        const totalReturnElement = document.getElementById('total-return');
        totalReturnElement.textContent = formatPercentage(totalReturn);
        totalReturnElement.className = `metric-value ${totalReturn >= 0 ? 'positive' : 'negative'}`;

        // Update positions table
        updatePositionsTable(data.positions || []);

        // Update portfolio chart data
        updatePortfolioChart(data);

        // Update allocation chart
        updateAllocationChart(data.positions || []);
    }

    // Update positions table
    function updatePositionsTable(positions) {
        const container = document.getElementById('positions-table');

        if (positions.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-inbox fa-2x mb-2"></i>
                    <p>No positions</p>
                </div>
            `;
            return;
        }

        const tableHtml = `
            <div class="table-responsive">
                <table class="table table-dark table-sm">
                    <thead>
                        <tr>
                            <th>Symbol</th>
                            <th>Quantity</th>
                            <th>Avg Price</th>
                            <th>Market Price</th>
                            <th>Market Value</th>
                            <th>P&L</th>
                            <th>P&L %</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${positions.map(pos => `
                            <tr>
                                <td><strong>${pos.symbol}</strong></td>
                                <td>${pos.quantity}</td>
                                <td>${formatCurrency(pos.average_price)}</td>
                                <td>${formatCurrency(pos.market_price)}</td>
                                <td>${formatCurrency(pos.market_value)}</td>
                                <td class="${pos.unrealized_pnl >= 0 ? 'positive' : 'negative'}">
                                    ${formatCurrency(pos.unrealized_pnl)}
                                </td>
                                <td class="${pos.unrealized_pnl_pct >= 0 ? 'positive' : 'negative'}">
                                    ${formatPercentage(pos.unrealized_pnl_pct)}
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;

        container.innerHTML = tableHtml;
    }

    // Update strategies display
    function updateStrategiesDisplay(data) {
        const container = document.getElementById('strategies-list');

        if (data.error) {
            showError('strategies-list', data.error);
            return;
        }

        const strategies = data.strategies || [];

        if (strategies.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-brain fa-2x mb-2"></i>
                    <p>No strategies configured</p>
                </div>
            `;
            return;
        }

        const strategiesHtml = strategies.map(strategy => `
            <div class="strategy-card mb-3 p-3 border rounded">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0">${strategy.name}</h6>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox"
                               id="strategy-${strategy.name}"
                               ${strategy.is_active ? 'checked' : ''}
                               onchange="toggleStrategy('${strategy.name}')">
                        <label class="form-check-label" for="strategy-${strategy.name}">
                            ${strategy.is_active ? 'Active' : 'Inactive'}
                        </label>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <small class="text-muted">Signals: ${strategy.total_signals}</small>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">Success Rate: ${strategy.success_rate.toFixed(1)}%</small>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-muted">Symbols: ${strategy.symbols.join(', ')}</small>
                </div>
            </div>
        `).join('');

        container.innerHTML = strategiesHtml;

        // Update recent signals
        updateRecentSignals(data.recent_signals || []);
    }

    // Update recent signals
    function updateRecentSignals(signals) {
        const container = document.getElementById('recent-signals');

        if (signals.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-broadcast-tower fa-2x mb-2"></i>
                    <p>No recent signals</p>
                </div>
            `;
            return;
        }

        const signalsHtml = signals.slice(0, 5).map(signal => {
            const signalClass = signal.signal_type === 'buy' ? 'signal-buy' :
                               signal.signal_type === 'sell' ? 'signal-sell' : 'signal-hold';

            return `
                <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                    <div>
                        <strong>${signal.symbol}</strong>
                        <span class="badge ${signalClass} signal-badge ms-2">${signal.signal_type.toUpperCase()}</span>
                    </div>
                    <div class="text-end">
                        <div>${formatCurrency(signal.price)}</div>
                        <small class="text-muted">Conf: ${(signal.confidence * 100).toFixed(0)}%</small>
                    </div>
                </div>
            `;
        }).join('');

        container.innerHTML = signalsHtml;
    }

    // Update risk display
    function updateRiskDisplay(data) {
        const container = document.getElementById('risk-status');

        if (data.error) {
            showError('risk-status', data.error);
            return;
        }

        const emergencyStop = data.emergency_stop_active;
        const riskLimits = data.risk_limits_breached;
        const portfolioMetrics = data.portfolio_metrics || {};

        const riskHtml = `
            <div class="row">
                <div class="col-12 mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Emergency Stop</span>
                        <span class="badge ${emergencyStop ? 'bg-danger' : 'bg-success'}">
                            ${emergencyStop ? 'ACTIVE' : 'Clear'}
                        </span>
                    </div>
                </div>
                <div class="col-12 mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Risk Limits</span>
                        <span class="badge ${riskLimits ? 'bg-warning' : 'bg-success'}">
                            ${riskLimits ? 'BREACHED' : 'OK'}
                        </span>
                    </div>
                </div>
                <div class="col-12 mb-2">
                    <small class="text-muted">Max Drawdown: ${(portfolioMetrics.max_drawdown_pct || 0).toFixed(2)}%</small>
                </div>
                <div class="col-12 mb-2">
                    <small class="text-muted">Active Stops: ${data.active_stops_count || 0}</small>
                </div>
                <div class="col-12">
                    <small class="text-muted">Recent Alerts: ${data.recent_alerts_count || 0}</small>
                </div>
            </div>
        `;

        container.innerHTML = riskHtml;
    }

    // Initialize charts
    function initializeCharts() {
        // Portfolio performance chart
        const portfolioCtx = document.getElementById('portfolio-chart').getContext('2d');
        portfolioChart = new Chart(portfolioCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Portfolio Value',
                    data: [],
                    borderColor: '#00d4aa',
                    backgroundColor: 'rgba(0, 212, 170, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: '#fff' }
                    }
                },
                scales: {
                    x: {
                        ticks: { color: '#fff' },
                        grid: { color: '#333' }
                    },
                    y: {
                        ticks: {
                            color: '#fff',
                            callback: function(value) {
                                return formatCurrency(value);
                            }
                        },
                        grid: { color: '#333' }
                    }
                }
            }
        });

        // Allocation pie chart
        const allocationCtx = document.getElementById('allocation-chart').getContext('2d');
        allocationChart = new Chart(allocationCtx, {
            type: 'doughnut',
            data: {
                labels: ['Cash'],
                datasets: [{
                    data: [100],
                    backgroundColor: ['#00d4aa', '#ff6b6b', '#4ecdc4', '#45b7d1', '#f9ca24', '#f0932b']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: '#fff' }
                    }
                }
            }
        });
    }

    // Update portfolio chart
    function updatePortfolioChart(data) {
        if (!portfolioChart) return;

        // Add current data point
        const now = new Date().toLocaleTimeString();
        portfolioData.push({
            time: now,
            value: data.total_value || 0
        });

        // Keep only last 20 data points
        if (portfolioData.length > 20) {
            portfolioData = portfolioData.slice(-20);
        }

        portfolioChart.data.labels = portfolioData.map(d => d.time);
        portfolioChart.data.datasets[0].data = portfolioData.map(d => d.value);
        portfolioChart.update('none');
    }

    // Update allocation chart
    function updateAllocationChart(positions) {
        if (!allocationChart) return;

        const labels = ['Cash'];
        const data = [100]; // Start with 100% cash
        const totalValue = positions.reduce((sum, pos) => sum + Math.abs(pos.market_value || 0), 0);

        if (totalValue > 0) {
            // Calculate cash percentage
            const cashValue = parseFloat(document.getElementById('cash-balance').textContent.replace(/[$,]/g, ''));
            const portfolioValue = parseFloat(document.getElementById('portfolio-value').textContent.replace(/[$,]/g, ''));

            if (portfolioValue > 0) {
                data[0] = (cashValue / portfolioValue) * 100;

                // Add position percentages
                positions.forEach(pos => {
                    if (Math.abs(pos.market_value) > 0) {
                        labels.push(pos.symbol);
                        data.push((Math.abs(pos.market_value) / portfolioValue) * 100);
                    }
                });
            }
        }

        allocationChart.data.labels = labels;
        allocationChart.data.datasets[0].data = data;
        allocationChart.update('none');
    }

    // Control functions
    async function initializeEngine() {
        try {
            const symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'SPY']; // Default symbols
            const result = await apiRequest('/api/initialize', {
                method: 'POST',
                body: JSON.stringify({ symbols })
            });

            if (result.success) {
                showAlert('Success', result.message, 'success');
                loadStatus();
            } else {
                showAlert('Error', result.message, 'danger');
            }
        } catch (error) {
            showAlert('Error', 'Failed to initialize engine: ' + error.message, 'danger');
        }
    }

    async function startTrading() {
        try {
            const result = await apiRequest('/api/start', { method: 'POST' });

            if (result.success) {
                showAlert('Success', result.message, 'success');
                loadStatus();
            } else {
                showAlert('Error', result.message, 'danger');
            }
        } catch (error) {
            showAlert('Error', 'Failed to start trading: ' + error.message, 'danger');
        }
    }

    async function stopTrading() {
        try {
            const result = await apiRequest('/api/stop', { method: 'POST' });

            if (result.success) {
                showAlert('Success', result.message, 'warning');
                loadStatus();
            } else {
                showAlert('Error', result.message, 'danger');
            }
        } catch (error) {
            showAlert('Error', 'Failed to stop trading: ' + error.message, 'danger');
        }
    }

    async function emergencyStop() {
        if (!confirm('Are you sure you want to trigger an emergency stop? This will halt all trading immediately.')) {
            return;
        }

        try {
            const result = await apiRequest('/api/emergency-stop', { method: 'POST' });

            if (result.success) {
                showAlert('Emergency Stop', result.message, 'danger');
                loadStatus();
                loadRiskStatus();
            } else {
                showAlert('Error', result.message, 'danger');
            }
        } catch (error) {
            showAlert('Error', 'Failed to trigger emergency stop: ' + error.message, 'danger');
        }
    }

    async function toggleStrategy(strategyName) {
        try {
            const result = await apiRequest(`/api/strategy/${strategyName}/toggle`, { method: 'POST' });

            if (result.success) {
                showAlert('Strategy Updated', result.message, 'info');
                loadStrategies();
            } else {
                showAlert('Error', result.message, 'danger');
                // Revert checkbox state
                const checkbox = document.getElementById(`strategy-${strategyName}`);
                if (checkbox) {
                    checkbox.checked = !checkbox.checked;
                }
            }
        } catch (error) {
            showAlert('Error', 'Failed to toggle strategy: ' + error.message, 'danger');
        }
    }

    // Show alert modal
    function showAlert(title, message, type = 'info') {
        const modal = new bootstrap.Modal(document.getElementById('alertModal'));
        document.getElementById('alertModalTitle').textContent = title;
        document.getElementById('alertModalBody').innerHTML = `
            <div class="alert alert-${type} mb-0">
                ${message}
            </div>
        `;
        modal.show();
    }

    // Handle real-time updates
    function handleRealTimeUpdate(data) {
        if (data.status && !data.status.error) {
            updateEngineStatus(data.status);
        }

        if (data.portfolio && !data.portfolio.error) {
            updatePortfolioDisplay(data.portfolio);
        }

        if (data.risk && !data.risk.error) {
            updateRiskDisplay(data.risk);
        }
    }

    // Auto-refresh data every 30 seconds
    setInterval(() => {
        if (!isConnected) {
            loadInitialData();
        }
    }, 30000);
</script>
{% endblock %}
